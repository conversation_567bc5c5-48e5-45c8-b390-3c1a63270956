import 'dart:io';
import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart' show kIsWeb;
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:medroid_app/models/chat_message.dart';
import 'package:medroid_app/services/api_service.dart';
import 'package:medroid_app/widgets/universal_chat_input.dart';
import 'package:medroid_app/widgets/chat_message.dart';
import 'package:medroid_app/widgets/auth_prompt_dialog.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:medroid_app/widgets/custom_bottom_navigation.dart';
import 'package:medroid_app/utils/app_colors.dart';
import 'package:medroid_app/screens/login_screen.dart';
import 'package:medroid_app/screens/main_navigation.dart';
import 'package:medroid_app/utils/responsive_utils.dart';

class AnonymousChatScreen extends StatefulWidget {
  const AnonymousChatScreen({Key? key}) : super(key: key);

  @override
  State<AnonymousChatScreen> createState() => _AnonymousChatScreenState();
}

class _AnonymousChatScreenState extends State<AnonymousChatScreen>
    with TickerProviderStateMixin {
  // Animation controller for chat activation
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  bool _chatActivated = false;
  final TextEditingController _messageController = TextEditingController();
  final ScrollController _scrollController = ScrollController();
  final List<ChatMessage> _messages = [];

  String? _conversationId;
  String? _anonymousId;
  String? _gender;
  String? _age;
  bool _isTyping = false;
  bool _isLoading = true;
  bool _hasError = false;
  bool _demographicsCollected = false;
  final bool _needsAuth = false; // Flag to track if user needs authentication
  bool _hasAppointmentIntent =
      false; // Flag to track appointment booking intent
  bool _userConsentedToAuth =
      false; // Flag to track if user consented to auth for appointment
  final FocusNode _inputFocusNode = FocusNode();

  // Store a reference to the ApiService
  late ApiService _apiService;
  bool _isInitialized = false;

  @override
  void initState() {
    super.initState();

    // Initialize animation controller
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 800),
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: Curves.easeInOut,
      ),
    );
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();

    // Get the ApiService from the context
    _apiService = RepositoryProvider.of<ApiService>(context);

    // Initialize chat after we have the ApiService, but only once
    if (!_isInitialized) {
      _isInitialized = true;
      _initializeChat();
    }
  }

  // Flag to track if the widget is being disposed
  bool _isDisposing = false;

  @override
  void dispose() {
    _isDisposing = true;
    _messageController.dispose();
    _scrollController.dispose();
    _animationController.dispose();

    // Safely dispose the focus node
    try {
      _inputFocusNode.unfocus();
      _inputFocusNode.dispose();
    } catch (e) {
      // Ignore errors if the focus node is already disposed
      debugPrint('Error disposing focus node: $e');
    }

    super.dispose();
  }

  Future<void> _initializeChat() async {
    setState(() {
      _isLoading = true;
      _hasError = false;
    });

    try {
      // Start a real anonymous chat conversation using the API service
      final chatData = await _apiService.startAnonymousChatConversation();

      // Check if still mounted after async operation
      if (!mounted) return;

      _conversationId = chatData['conversation_id'];
      _anonymousId = chatData['anonymous_id'];

      debugPrint('Created anonymous chat session:');
      debugPrint('Conversation ID: $_conversationId');
      debugPrint('Anonymous ID: $_anonymousId');

      // Demographics will be collected when the user starts chatting
      _demographicsCollected = false;
    } catch (e) {
      debugPrint('Error initializing anonymous chat: $e');
      if (!mounted) return;

      setState(() {
        _hasError = true;
      });
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });

        // Set focus to the input field when chat is initialized
        if (!_hasError) {
          // Use a small delay to ensure the UI is fully built
          Future.delayed(const Duration(milliseconds: 300), () {
            if (mounted && !_isDisposing) {
              _inputFocusNode.requestFocus();
            }
          });
        }
      }
    }
  }

  void _addUserMessage(String text) {
    setState(() {
      _messages.add(ChatMessage(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        role: 'user',
        content: text,
        timestamp: DateTime.now(),
        contentType: MessageContentType.text,
      ));
    });
  }

  void _addAssistantMessage(String text) {
    setState(() {
      _messages.add(ChatMessage(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        role: 'assistant',
        content: text,
        timestamp: DateTime.now(),
        contentType: MessageContentType.text,
      ));
    });
  }

  // Show dialog to collect demographic information
  Future<void> _showDemographicsDialog() async {
    // Initialize without default values to allow user selection
    String? selectedGender;
    String? selectedAge;
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final isDesktop = ResponsiveUtils.isDesktop(context);

    // Use ValueNotifier to track selection state
    final genderNotifier = ValueNotifier<String?>(null);
    final ageNotifier = ValueNotifier<String?>(null);

    // Make sure to dispose the notifiers when done
    void disposeNotifiers() {
      genderNotifier.dispose();
      ageNotifier.dispose();
    }

    await showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) {
        return StatefulBuilder(
          builder: (context, setState) {
            // Update local variables when notifiers change
            genderNotifier.addListener(() {
              setState(() {
                selectedGender = genderNotifier.value;
              });
            });

            ageNotifier.addListener(() {
              setState(() {
                selectedAge = ageNotifier.value;
              });
            });

            return Dialog(
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(24),
              ),
              elevation: 0,
              backgroundColor: Colors.transparent,
              child: Container(
                width: isDesktop ? 600 : null,
                padding: const EdgeInsets.all(24),
                decoration: BoxDecoration(
                  color: isDarkMode ? const Color(0xFF212428) : Colors.white,
                  borderRadius: BorderRadius.circular(24),
                  // More elegant shadow with layered effect
                  boxShadow: [
                    // First shadow - closer, sharper
                    BoxShadow(
                      color: Colors.black.withAlpha(10),
                      blurRadius: 10,
                      spreadRadius: 0,
                      offset: const Offset(0, 2),
                    ),
                    // Second shadow - further, softer
                    BoxShadow(
                      color: Colors.black.withAlpha(5),
                      blurRadius: 20,
                      spreadRadius: 0,
                      offset: const Offset(0, 6),
                    ),
                  ],
                  // Subtle border
                  border: Border.all(
                    color: isDarkMode
                        ? Colors.white.withAlpha(15)
                        : const Color(0xFFE6F7F5),
                    width: 1,
                  ),
                ),
                child: isDesktop
                    ? _buildDesktopDemographicsContent(
                        setState,
                        isDarkMode,
                        selectedGender,
                        selectedAge,
                        genderNotifier,
                        ageNotifier)
                    : _buildMobileDemographicsContent(
                        setState,
                        isDarkMode,
                        selectedGender,
                        selectedAge,
                        genderNotifier,
                        ageNotifier),
              ),
            );
          },
        );
      },
    );

    // Dispose the notifiers to prevent memory leaks
    disposeNotifiers();

    // Update the state with collected demographics
    if (mounted) {
      setState(() {
        // Handle null values safely - if user didn't select anything, use 'prefer_not_to_say'
        if (selectedGender != null) {
          _gender = selectedGender!.toLowerCase().replaceAll(' ', '_');
        } else {
          _gender = 'prefer_not_to_say';
        }
        _age = selectedAge ?? 'prefer_not_to_say';
        _demographicsCollected = true;
      });

      // Save the demographic information to SharedPreferences
      if (_conversationId != null && _anonymousId != null) {
        final prefs = await SharedPreferences.getInstance();
        final Map<String, dynamic> data = {
          'conversation_id': _conversationId!,
          'anonymous_id': _anonymousId!,
          'timestamp': DateTime.now().toIso8601String(),
        };

        if (_gender != null) {
          data['gender'] = _gender;
        }

        if (_age != null) {
          data['age'] = _age;
        }

        await prefs.setString('anonymous_chat_data', json.encode(data));
      }
    }
  }

  // Build desktop layout for demographics dialog
  Widget _buildDesktopDemographicsContent(
      StateSetter setState,
      bool isDarkMode,
      String? selectedGender,
      String? selectedAge,
      ValueNotifier<String?> genderNotifier,
      ValueNotifier<String?> ageNotifier) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        // Header with gradient
        Container(
          padding: const EdgeInsets.only(bottom: 16),
          decoration: BoxDecoration(
            border: Border(
              bottom: BorderSide(
                color: isDarkMode
                    ? Colors.grey.withAlpha(50)
                    : Colors.grey.withAlpha(25),
                width: 1,
              ),
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              ShaderMask(
                shaderCallback: (bounds) =>
                    AppColors.getPrimaryGradient().createShader(bounds),
                child: Text(
                  'Help us personalize your experience',
                  style: GoogleFonts.inter(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
              ),
              const SizedBox(height: 8),
              Text(
                'To provide more accurate health information, please share:',
                style: GoogleFonts.inter(
                  fontSize: 14,
                  color: isDarkMode ? Colors.grey[300] : Colors.grey[700],
                ),
              ),
            ],
          ),
        ),

        const SizedBox(height: 20),

        // Two-column layout for desktop
        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Gender selection column
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Gender',
                    style: GoogleFonts.inter(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: isDarkMode ? Colors.white : Colors.black87,
                    ),
                  ),
                  const SizedBox(height: 12),

                  // Gender options in a column for desktop
                  Column(
                    children: [
                      _buildGenderOption(
                          'Male', 'male', Icons.male_rounded, selectedGender,
                          (value) {
                        genderNotifier.value = value;
                      }, isDarkMode),
                      const SizedBox(height: 8),
                      _buildGenderOption('Female', 'female',
                          Icons.female_rounded, selectedGender, (value) {
                        genderNotifier.value = value;
                      }, isDarkMode),
                      const SizedBox(height: 8),
                      _buildGenderOption('Other', 'other', Icons.person_rounded,
                          selectedGender, (value) {
                        genderNotifier.value = value;
                      }, isDarkMode),
                      const SizedBox(height: 8),
                      _buildGenderOption(
                          'Prefer not to say',
                          'prefer_not_to_say',
                          Icons.not_interested_rounded,
                          selectedGender, (value) {
                        genderNotifier.value = value;
                      }, isDarkMode),
                    ],
                  ),
                ],
              ),
            ),

            const SizedBox(width: 24),

            // Age group selection column
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Age group',
                    style: GoogleFonts.inter(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: isDarkMode ? Colors.white : Colors.black87,
                    ),
                  ),
                  const SizedBox(height: 12),

                  // Age options in a column for desktop
                  Column(
                    children: [
                      _buildAgeOption('Under 18', selectedAge, (value) {
                        ageNotifier.value = value;
                      }, isDarkMode),
                      const SizedBox(height: 8),
                      _buildAgeOption('18-30', selectedAge, (value) {
                        ageNotifier.value = value;
                      }, isDarkMode),
                      const SizedBox(height: 8),
                      _buildAgeOption('31-45', selectedAge, (value) {
                        ageNotifier.value = value;
                      }, isDarkMode),
                      const SizedBox(height: 8),
                      _buildAgeOption('46-60', selectedAge, (value) {
                        ageNotifier.value = value;
                      }, isDarkMode),
                      const SizedBox(height: 8),
                      _buildAgeOption('Over 60', selectedAge, (value) {
                        ageNotifier.value = value;
                      }, isDarkMode),
                      const SizedBox(height: 8),
                      _buildAgeOption('Prefer not to say', selectedAge,
                          (value) {
                        ageNotifier.value = value;
                      }, isDarkMode),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),

        const SizedBox(height: 24),

        // Continue button with gradient - more prominent
        Container(
          height: 56, // Increased height
          decoration: BoxDecoration(
            gradient: AppColors.getPrimaryGradient(),
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: AppColors.tealSurge
                    .withAlpha(100), // Increased shadow opacity
                blurRadius: 10,
                spreadRadius: 1,
                offset: const Offset(0, 3),
              ),
            ],
          ),
          child: Material(
            color: Colors.transparent,
            child: InkWell(
              borderRadius: BorderRadius.circular(12),
              splashColor: Colors.white.withAlpha(30),
              highlightColor: Colors.white.withAlpha(20),
              onTap: () {
                // Let the outer code handle null values
                Navigator.of(context).pop();
              },
              child: Center(
                child: Text(
                  'Continue',
                  style: GoogleFonts.inter(
                    fontSize: 18, // Increased font size
                    fontWeight: FontWeight.w600,
                    color: Colors.white,
                    letterSpacing: 0.5, // Added letter spacing
                  ),
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }

  // Build mobile layout for demographics dialog
  Widget _buildMobileDemographicsContent(
      StateSetter setState,
      bool isDarkMode,
      String? selectedGender,
      String? selectedAge,
      ValueNotifier<String?> genderNotifier,
      ValueNotifier<String?> ageNotifier) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        // Header with gradient
        Container(
          padding: const EdgeInsets.only(bottom: 16),
          decoration: BoxDecoration(
            border: Border(
              bottom: BorderSide(
                color: isDarkMode
                    ? Colors.grey.withAlpha(50)
                    : Colors.grey.withAlpha(25),
                width: 1,
              ),
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              ShaderMask(
                shaderCallback: (bounds) =>
                    AppColors.getPrimaryGradient().createShader(bounds),
                child: Text(
                  'Help us personalize your experience',
                  style: GoogleFonts.inter(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
              ),
              const SizedBox(height: 8),
              Text(
                'To provide more accurate health information, please share:',
                style: GoogleFonts.inter(
                  fontSize: 14,
                  color: isDarkMode ? Colors.grey[300] : Colors.grey[700],
                ),
              ),
            ],
          ),
        ),

        const SizedBox(height: 20),

        // Gender selection
        Text(
          'Gender',
          style: GoogleFonts.inter(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: isDarkMode ? Colors.white : Colors.black87,
          ),
        ),
        const SizedBox(height: 12),

        // Gender options in a grid with consistent sizing
        GridView.count(
          crossAxisCount: 2,
          shrinkWrap: true,
          childAspectRatio: 3.0,
          crossAxisSpacing: 10,
          mainAxisSpacing: 10,
          physics: const NeverScrollableScrollPhysics(),
          children: [
            _buildGenderOption(
                'Male', 'male', Icons.male_rounded, selectedGender, (value) {
              genderNotifier.value = value;
            }, isDarkMode),
            _buildGenderOption(
                'Female', 'female', Icons.female_rounded, selectedGender,
                (value) {
              genderNotifier.value = value;
            }, isDarkMode),
            _buildGenderOption(
                'Other', 'other', Icons.person_rounded, selectedGender,
                (value) {
              genderNotifier.value = value;
            }, isDarkMode),
            _buildGenderOption('Prefer not to say', 'prefer_not_to_say',
                Icons.not_interested_rounded, selectedGender, (value) {
              genderNotifier.value = value;
            }, isDarkMode),
          ],
        ),

        const SizedBox(height: 24),

        // Age group selection
        Text(
          'Age group',
          style: GoogleFonts.inter(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: isDarkMode ? Colors.white : Colors.black87,
          ),
        ),
        const SizedBox(height: 12),

        // Age options in a grid with consistent sizing
        GridView.count(
          crossAxisCount: 2,
          shrinkWrap: true,
          childAspectRatio: 3.0,
          crossAxisSpacing: 10,
          mainAxisSpacing: 10,
          physics: const NeverScrollableScrollPhysics(),
          children: [
            _buildAgeOption('Under 18', selectedAge, (value) {
              ageNotifier.value = value;
            }, isDarkMode),
            _buildAgeOption('18-30', selectedAge, (value) {
              ageNotifier.value = value;
            }, isDarkMode),
            _buildAgeOption('31-45', selectedAge, (value) {
              ageNotifier.value = value;
            }, isDarkMode),
            _buildAgeOption('46-60', selectedAge, (value) {
              ageNotifier.value = value;
            }, isDarkMode),
            _buildAgeOption('Over 60', selectedAge, (value) {
              ageNotifier.value = value;
            }, isDarkMode),
            _buildAgeOption('Prefer not to say', selectedAge, (value) {
              ageNotifier.value = value;
            }, isDarkMode),
          ],
        ),

        const SizedBox(height: 24),

        // Continue button with gradient - more prominent
        Container(
          height: 56, // Increased height
          decoration: BoxDecoration(
            gradient: AppColors.getPrimaryGradient(),
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: AppColors.tealSurge
                    .withAlpha(100), // Increased shadow opacity
                blurRadius: 10,
                spreadRadius: 1,
                offset: const Offset(0, 3),
              ),
            ],
          ),
          child: Material(
            color: Colors.transparent,
            child: InkWell(
              borderRadius: BorderRadius.circular(12),
              splashColor: Colors.white.withAlpha(30),
              highlightColor: Colors.white.withAlpha(20),
              onTap: () {
                // Let the outer code handle null values
                Navigator.of(context).pop();
              },
              child: Center(
                child: Text(
                  'Continue',
                  style: GoogleFonts.inter(
                    fontSize: 18, // Increased font size
                    fontWeight: FontWeight.w600,
                    color: Colors.white,
                    letterSpacing: 0.5, // Added letter spacing
                  ),
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }

  // Build a gender option with icon
  Widget _buildGenderOption(String label, String value, IconData icon,
      String? selectedGender, Function(String) onSelected, bool isDarkMode) {
    final isSelected = selectedGender == value;

    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: () => onSelected(value),
        borderRadius: BorderRadius.circular(12),
        splashColor: AppColors.tealSurge.withAlpha(50),
        highlightColor: AppColors.tealSurge.withAlpha(30),
        child: Container(
          padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 12),
          decoration: BoxDecoration(
            color: isSelected
                ? (isDarkMode
                    ? AppColors.tealSurge.withAlpha(80)
                    : AppColors.tealSurge.withAlpha(
                        40)) // Increased opacity for better visibility
                : (isDarkMode ? Colors.grey.shade800 : Colors.grey.shade100),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: isSelected
                  ? AppColors.tealSurge // Use teal for border
                  : (isDarkMode ? Colors.grey.shade700 : Colors.grey.shade300),
              width:
                  isSelected ? 3 : 1, // Increased width for better visibility
            ),
            // Add shadow for selected items
            boxShadow: isSelected
                ? [
                    BoxShadow(
                      color: AppColors.tealSurge.withAlpha(40),
                      blurRadius: 4,
                      spreadRadius: 1,
                    )
                  ]
                : null,
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                icon,
                color: isSelected
                    ? AppColors.tealSurge // Use teal for icon
                    : (isDarkMode
                        ? Colors.grey.shade400
                        : Colors.grey.shade700),
                size: 18,
              ),
              const SizedBox(width: 6),
              Expanded(
                child: Text(
                  label,
                  overflow: TextOverflow.ellipsis,
                  maxLines: 1,
                  style: GoogleFonts.inter(
                    fontSize: 13,
                    fontWeight: isSelected ? FontWeight.w600 : FontWeight.w400,
                    color: isSelected
                        ? (isDarkMode
                            ? Colors.white
                            : AppColors.tealSurge) // Use teal for text
                        : (isDarkMode
                            ? Colors.grey.shade300
                            : Colors.grey.shade800),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // Build an age option
  Widget _buildAgeOption(String label, String? selectedAge,
      Function(String) onSelected, bool isDarkMode) {
    final isSelected = selectedAge == label;

    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: () => onSelected(label),
        borderRadius: BorderRadius.circular(12),
        splashColor: AppColors.tealSurge.withAlpha(50),
        highlightColor: AppColors.tealSurge.withAlpha(30),
        child: Container(
          padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 12),
          decoration: BoxDecoration(
            color: isSelected
                ? (isDarkMode
                    ? AppColors.tealSurge.withAlpha(80)
                    : AppColors.tealSurge.withAlpha(
                        40)) // Increased opacity for better visibility
                : (isDarkMode ? Colors.grey.shade800 : Colors.grey.shade100),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: isSelected
                  ? AppColors.tealSurge // Use teal for border
                  : (isDarkMode ? Colors.grey.shade700 : Colors.grey.shade300),
              width:
                  isSelected ? 3 : 1, // Increased width for better visibility
            ),
            // Add shadow for selected items
            boxShadow: isSelected
                ? [
                    BoxShadow(
                      color: AppColors.tealSurge.withAlpha(40),
                      blurRadius: 4,
                      spreadRadius: 1,
                    )
                  ]
                : null,
          ),
          child: Center(
            child: Text(
              label,
              overflow: TextOverflow.ellipsis,
              maxLines: 1,
              style: GoogleFonts.inter(
                fontSize: 13,
                fontWeight: isSelected ? FontWeight.w600 : FontWeight.w400,
                color: isSelected
                    ? (isDarkMode
                        ? Colors.white
                        : AppColors.tealSurge) // Use teal for text
                    : (isDarkMode
                        ? Colors.grey.shade300
                        : Colors.grey.shade800),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Future<void> _sendMessage(String text, {File? imageFile}) async {
    if (text.trim().isEmpty && imageFile == null) return;
    if (_conversationId == null || _anonymousId == null) return;

    // Check if we need to collect demographic information
    if (!_demographicsCollected) {
      await _showDemographicsDialog();
    }

    _addUserMessage(text);

    setState(() {
      _isTyping = true;
    });

    // Request focus immediately after sending to keep cursor in input field
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted && !_isDisposing) {
        _inputFocusNode.requestFocus();
      }
    });

    _scrollToBottom();

    // Check for appointment-related keywords
    bool containsAppointmentKeywords =
        text.toLowerCase().contains('appointment') ||
            text.toLowerCase().contains('book') ||
            text.toLowerCase().contains('schedule') ||
            text.toLowerCase().contains('doctor');

    // Check if user is consenting to appointment booking after AI suggested it
    bool userConsentedToBooking =
        _hasAppointmentIntent && _userConsentedToBooking(text);

    // Debug logging for user consent detection
    if (userConsentedToBooking) {
      debugPrint('=== USER CONSENT DETECTED ===');
      debugPrint('User Message: $text');
      debugPrint('_hasAppointmentIntent: $_hasAppointmentIntent');
      debugPrint('User consented to booking: $userConsentedToBooking');
    }

    // Save conversation ID and anonymous ID before async gap
    final conversationId = _conversationId!;
    final anonymousId = _anonymousId!;
    final gender = _gender;
    final age = _age;

    try {
      if (userConsentedToBooking) {
        // User consented to appointment booking - show auth prompt immediately WITHOUT sending to AI
        if (!mounted) return;

        debugPrint('=== INTERCEPTING USER CONSENT - NOT SENDING TO AI ===');

        _addAssistantMessage(
            'Perfect! To proceed with booking your appointment and view available time slots, '
            'you\'ll need to sign in or create an account first.');

        setState(() {
          _hasAppointmentIntent = true;
          _userConsentedToAuth = true;
        });

        // Show auth prompt immediately
        Future.delayed(const Duration(milliseconds: 500), () {
          if (mounted) {
            _showAuthPrompt(reason: 'Please sign in to book an appointment');
          }
        });
      } else if (containsAppointmentKeywords) {
        // If appointment keywords are detected, handle it specially
        if (!mounted) return;

        _addAssistantMessage(
            'I understand you\'d like to book an appointment. To proceed with booking '
            'and view available time slots, you\'ll need to sign in or create an account first. '
            'Would you like to sign in or register now?');

        setState(() {
          _hasAppointmentIntent = true;
          _userConsentedToAuth = true;
        });

        // Show auth prompt
        Future.delayed(const Duration(milliseconds: 500), () {
          if (mounted) {
            _showAuthPrompt(reason: 'Please sign in to book an appointment');
          }
        });
      } else {
        // Add a loading message to show AI is thinking
        _addAssistantMessage('...');
        final loadingMessageIndex = _messages.length - 1;

        // Send the message to the API and get a response
        try {
          final response = await _apiService.sendAnonymousChatMessage(
            conversationId,
            anonymousId,
            text,
            gender: gender,
            age: age,
          );

          // Check if still mounted after async operation
          if (!mounted) return;

          // Check if the AI response indicates appointment booking but is incomplete
          final aiMessage = response['message'] as String? ?? '';
          final isIncompleteBookingResponse =
              _isIncompleteBookingResponse(aiMessage);

          // Replace the loading message with the actual response
          setState(() {
            _messages[loadingMessageIndex] = ChatMessage(
              id: DateTime.now().millisecondsSinceEpoch.toString(),
              role: 'assistant',
              content: isIncompleteBookingResponse
                  ? 'I\'d be happy to help you schedule an appointment! To proceed with booking and view available time slots, you\'ll need to sign in or create an account first.'
                  : response['message'],
              timestamp: DateTime.now(),
              contentType: MessageContentType.text,
            );
          });

          // Check if the response requires authentication, has appointment intent, user gave explicit consent, or response is incomplete booking
          if (response['requires_auth'] == true ||
              response['has_appointment_intent'] == true ||
              response['has_explicit_consent'] == true ||
              isIncompleteBookingResponse) {
            debugPrint('=== TRIGGERING AUTH PROMPT ===');
            debugPrint(
                'Reason: requires_auth=${response['requires_auth']}, has_appointment_intent=${response['has_appointment_intent']}, has_explicit_consent=${response['has_explicit_consent']}, isIncomplete=$isIncompleteBookingResponse');

            setState(() {
              _hasAppointmentIntent = true;
            });

            // Show auth prompt with a small delay
            Future.delayed(const Duration(milliseconds: 500), () {
              if (mounted) {
                _showAuthPrompt(
                    reason: 'Please sign in to book an appointment');
              }
            });
          }

          // Check if we have available appointment slots
          if (response['available_slots'] != null &&
              response['available_slots'] is List &&
              (response['available_slots'] as List).isNotEmpty) {
            setState(() {
              _hasAppointmentIntent = true;
            });

            // Save the appointment slots for after authentication
            await _saveAppointmentSlotsForLater(
                response['available_slots'] as List);

            // Show auth prompt instead of appointment slots
            Future.delayed(const Duration(milliseconds: 500), () {
              if (mounted) {
                _showAuthPrompt(
                    reason:
                        'Please sign in to view and book available appointment slots');
              }
            });
          }
        } catch (e) {
          // Check if still mounted after error
          if (!mounted) return;

          // Handle different types of errors
          String errorMessage;
          if (e.toString().contains('timed out')) {
            errorMessage =
                'I apologize, but my response is taking longer than expected. '
                'This might be due to high server load. Please try asking your question again.';
          } else if (e.toString().contains('connection') ||
              e.toString().contains('network')) {
            errorMessage = 'I\'m having trouble connecting to our servers. '
                'Please check your internet connection and try again.';
          } else {
            errorMessage =
                'I apologize, but I encountered an error processing your request. '
                'Please try again or rephrase your question.';
          }

          // Replace the loading message with the error message
          setState(() {
            _messages[loadingMessageIndex] = ChatMessage(
              id: DateTime.now().millisecondsSinceEpoch.toString(),
              role: 'assistant',
              content: errorMessage,
              timestamp: DateTime.now(),
              contentType: MessageContentType.text,
            );
          });

          // Log the error for debugging
          debugPrint('Error in anonymous chat: $e');
        }
      }
    } catch (e) {
      // Check if still mounted after async operation
      if (!mounted) return;

      debugPrint('Error sending message: $e');
      _addAssistantMessage(
          'Sorry, I encountered an error processing your message. Please try again.');
    } finally {
      if (mounted) {
        setState(() {
          _isTyping = false;
        });

        _scrollToBottom();

        // Request focus back to the input field to keep cursor active
        WidgetsBinding.instance.addPostFrameCallback((_) {
          if (mounted && !_isDisposing) {
            _inputFocusNode.requestFocus();
          }
        });
      }
    }
  }

  // Note: Provider selection dialog is now shown after authentication in MainNavigation

  // Note: Appointment selection is now handled in MainNavigation after authentication

  // Note: Appointment details are now saved in MainNavigation after authentication

  void _showAuthPrompt({String? reason}) {
    showDialog(
      context: context,
      barrierDismissible: true, // Allow dismissing by tapping outside
      builder: (context) => AuthPromptDialog(
        reason: reason,
        onLogin: () {
          Navigator.of(context).pop();
          _navigateToAuth(
              reason: reason ?? 'Please sign in to access all features',
              isLogin: true);
        },
        onRegister: () {
          Navigator.of(context).pop();
          _navigateToAuth(
              reason: reason ?? 'Please register to access all features',
              isLogin: false);
        },
      ),
    ).then((result) {
      // Handle when user dismisses the dialog without choosing login/register
      if (result == null) {
        _handleAuthPromptDismissed();
      }
    });
  }

  void _handleAuthPromptDismissed() {
    // User dismissed the auth prompt, continue with normal chat
    // Add a message to acknowledge their choice and continue helping
    _addAssistantMessage(
        'No problem! I\'m still here to help answer your health questions. '
        'If you decide you\'d like to book an appointment later, just let me know!');

    // Reset appointment intent flags so they can try again later
    setState(() {
      _hasAppointmentIntent = false;
      _userConsentedToAuth = false;
    });
  }

  Future<void> _saveAppointmentSlotsForLater(List availableSlots) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(
        'available_appointment_slots', json.encode(availableSlots));
    await prefs.setBool('has_pending_appointment_slots', true);
    debugPrint(
        'Saved ${availableSlots.length} appointment slots for after authentication');
  }

  /// Detects if the AI response indicates appointment booking but is incomplete/truncated
  bool _isIncompleteBookingResponse(String message) {
    final lowerMessage = message.toLowerCase();

    // Check for appointment booking indicators
    final hasBookingIntent = lowerMessage.contains('appointment') ||
        lowerMessage.contains('schedule') ||
        lowerMessage.contains('book') ||
        lowerMessage.contains('consultation');

    if (!hasBookingIntent) return false;

    // Check for signs of incomplete response
    final isIncomplete = message.endsWith('To prepare, I\'ll create a') ||
        message.endsWith('I\'ll create a') ||
        message.endsWith('To prepare,') ||
        message.endsWith('I\'ll book') ||
        message.endsWith('I\'ll help') ||
        message.endsWith('Let me') ||
        message.endsWith('Also, I\'ll generate a') ||
        message.endsWith('I\'ll generate a') ||
        message.contains('Also, I\'ll generate a \n\nWould you like to book') ||
        message.length <
            50 || // Very short responses after booking confirmation
        (lowerMessage.contains('book') &&
            message.trim().split(' ').length < 10);

    return isIncomplete;
  }

  /// Detects if user message indicates appointment booking consent
  bool _userConsentedToBooking(String userMessage) {
    final lowerMessage = userMessage.toLowerCase().trim();

    // Check if user is confirming appointment booking
    final consentWords = [
      'yes',
      'yeah',
      'yep',
      'sure',
      'ok',
      'okay',
      'alright',
      'fine',
      'please',
      'i would like',
      'i\'d like',
      'book it',
      'schedule it',
      'let\'s do it',
      'go ahead',
      'proceed',
      'continue'
    ];

    return consentWords.any((word) => lowerMessage.contains(word)) ||
        lowerMessage == 'y' ||
        lowerMessage.startsWith('yes') ||
        lowerMessage.startsWith('sure') ||
        lowerMessage.startsWith('ok');
  }

  // Start a new chat by clearing the current conversation
  Future<void> _startNewChat() async {
    // Show confirmation dialog
    final bool? confirm = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Start New Chat'),
        content: const Text(
            'Are you sure you want to start a new chat? Your current conversation will be cleared.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('Start New Chat'),
          ),
        ],
      ),
    );

    if (confirm != true) return;

    // We'll use the stored _apiService reference

    setState(() {
      _isLoading = true;
    });

    try {
      // Start a real anonymous chat conversation using the API service
      final chatData = await _apiService.startAnonymousChatConversation();

      // Check if still mounted after async operation
      if (!mounted) return;

      _conversationId = chatData['conversation_id'];
      _anonymousId = chatData['anonymous_id'];

      setState(() {
        _messages.clear();
        _isTyping = false;
        _chatActivated = false;
        _isLoading = false;
        _demographicsCollected = false;
      });

      // Show a snackbar to confirm
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Started a new chat')),
      );
    } catch (e) {
      // Check if still mounted after async operation
      if (!mounted) return;

      setState(() {
        _isLoading = false;
      });

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error starting new chat: $e')),
      );
    }
  }

  void _navigateToAuth({String? reason, bool isLogin = true}) async {
    // Cancel any pending focus requests to avoid FocusNode errors
    _inputFocusNode.unfocus();

    try {
      // Prepare chat data for transfer without trying to create a new conversation
      await _prepareAnonymousChatForTransfer();

      // Then navigate to login screen if still mounted
      if (mounted) {
        // Show a snackbar with the reason if provided
        if (reason != null) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(reason),
              duration: const Duration(seconds: 3),
            ),
          );
        }

        Navigator.of(context).pushAndRemoveUntil(
          MaterialPageRoute(
            builder: (context) => LoginScreen(
              initialMode: isLogin ? 'login' : 'register',
              onLoginSuccess: (token) {
                // Navigate to main screen after successful login
                // The token parameter ensures we have the latest token
                debugPrint(
                    'Login successful, navigating to main screen with token: $token');

                Navigator.of(context).pushAndRemoveUntil(
                  MaterialPageRoute(builder: (_) => const MainNavigation()),
                  (route) => false, // Remove all previous routes
                );
              },
            ),
          ),
          (route) =>
              false, // Remove all previous routes to avoid duplicate navigation bars
        );
      }
    } catch (e) {
      debugPrint('CHAT TRANSFER ERROR: Error navigating to auth: $e');

      // Even if transfer fails, still navigate to login
      if (mounted) {
        Navigator.of(context).pushAndRemoveUntil(
          MaterialPageRoute(
            builder: (context) => LoginScreen(
              initialMode: isLogin ? 'login' : 'register',
              onLoginSuccess: (token) {
                Navigator.of(context).pushAndRemoveUntil(
                  MaterialPageRoute(builder: (_) => const MainNavigation()),
                  (route) => false,
                );
              },
            ),
          ),
          (route) => false,
        );
      }
    }
  }

  // Prepare anonymous chat data for transfer without trying to create a new conversation
  Future<void> _prepareAnonymousChatForTransfer() async {
    if (_conversationId == null || _anonymousId == null || _messages.isEmpty) {
      debugPrint('CHAT TRANSFER: No chat data to prepare for transfer');
      return;
    }

    try {
      debugPrint('CHAT TRANSFER: Preparing anonymous chat data for transfer');

      // Get shared preferences
      final prefs = await SharedPreferences.getInstance();

      // Check if there's a pending appointment to transfer
      final pendingAppointmentJson = prefs.getString('pending_appointment');
      bool hasAppointmentContext = false;

      if (pendingAppointmentJson != null) {
        try {
          final appointmentDetails =
              json.decode(pendingAppointmentJson) as Map<String, dynamic>;
          hasAppointmentContext = appointmentDetails['needs_booking'] == true;

          // Add appointment context to the conversation
          if (hasAppointmentContext) {
            debugPrint(
                'CHAT TRANSFER: Including appointment context: $appointmentDetails');
          }
        } catch (e) {
          debugPrint('CHAT TRANSFER: Error parsing pending appointment: $e');
        }
      }

      // Prepare a summary of the previous conversation
      String conversationSummary = 'Previous anonymous chat summary:\n';

      // Get the last 8 messages or all if less than 8
      int messageCount = _messages.length;
      int startIndex = messageCount > 8 ? messageCount - 8 : 0;

      for (int i = startIndex; i < messageCount; i++) {
        final message = _messages[i];
        conversationSummary +=
            "${message.role == 'user' ? 'Me' : 'Assistant'}: ${message.content}\n";
      }

      // Add any health concerns or context that might have been identified
      if (_messages.any((m) =>
          m.content.toLowerCase().contains('headache') ||
          m.content.toLowerCase().contains('pain') ||
          m.content.toLowerCase().contains('symptom'))) {
        conversationSummary +=
            "\nI was discussing health concerns related to symptoms I'm experiencing.";
      }

      // Add appointment booking context if detected
      bool hasAppointmentIntent = _hasAppointmentIntent ||
          _messages.any((m) =>
              m.content.toLowerCase().contains('appointment') ||
              m.content.toLowerCase().contains('book') ||
              m.content.toLowerCase().contains('doctor'));

      if (hasAppointmentIntent || hasAppointmentContext) {
        // Set a flag to indicate we need to continue with appointment booking
        await prefs.setBool('continue_appointment_booking', true);

        // Set a flag to trigger appointment booking immediately after login
        await prefs.setBool('trigger_appointment_booking_after_login', true);

        if (hasAppointmentContext && pendingAppointmentJson != null) {
          // Add specific appointment details if available
          final appointmentDetails =
              json.decode(pendingAppointmentJson) as Map<String, dynamic>;
          conversationSummary +=
              '\nI was in the process of booking an appointment with '
              'Dr. ${appointmentDetails['provider_name']} on ${appointmentDetails['date']} '
              'at ${appointmentDetails['time']}. Please help me complete this booking.';
        } else {
          conversationSummary +=
              '\nI was interested in booking an appointment. Please help me continue with the booking process.';
        }
      }

      // Add demographic information if available
      if (_gender != null || _age != null) {
        conversationSummary += '\n\nDemographic information:';
        if (_gender != null) {
          conversationSummary += '\nGender: $_gender';
        }
        if (_age != null) {
          conversationSummary += '\nAge group: $_age';
        }
      }

      // Add consent information if user has consented to auth for appointment
      if (_userConsentedToAuth) {
        conversationSummary +=
            '\n\nThe user has already consented to booking an appointment. Please continue with the appointment booking process.';
      }

      // Store the conversation summary
      await prefs.setString('anonymous_chat_summary', conversationSummary);

      // Set a flag to indicate we're coming from anonymous chat
      await prefs.setBool('coming_from_anonymous_chat', true);

      // Store all messages for complete transfer
      final List<Map<String, dynamic>> messageData = _messages
          .map((m) => {
                'role': m.role,
                'content': m.content,
                'timestamp': m.timestamp.toIso8601String(),
              })
          .toList();

      await prefs.setString('transferred_messages', json.encode(messageData));

      // Save a timestamp for the transfer
      await prefs.setString(
          'transfer_timestamp', DateTime.now().toIso8601String());

      // Save flag to indicate if we need to continue appointment booking after login
      await prefs.setBool('continue_appointment_booking',
          hasAppointmentIntent || hasAppointmentContext);

      // Save demographic information to transfer to the authenticated user
      if (_gender != null) {
        await prefs.setString('transfer_gender', _gender!);
      }

      if (_age != null) {
        await prefs.setString('transfer_age', _age!);
      }

      // Save the original conversation ID and anonymous ID
      await prefs.setString(
          'original_anonymous_conversation_id', _conversationId!);
      await prefs.setString('original_anonymous_id', _anonymousId!);

      // Mark the transfer as ready
      await prefs.setBool('anonymous_chat_transfer_ready', true);

      debugPrint('CHAT TRANSFER: Successfully prepared chat data for transfer');
      debugPrint(
          'CHAT TRANSFER: Saved ${_messages.length} messages for transfer');
      debugPrint(
          'CHAT TRANSFER: Appointment context: ${hasAppointmentIntent || hasAppointmentContext}');
    } catch (e) {
      debugPrint('CHAT TRANSFER ERROR: $e');
      rethrow; // Rethrow to handle in the calling method
    }
  }

  void _scrollToBottom() {
    // Use a slightly longer delay to ensure the UI has updated with new messages
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_scrollController.hasClients) {
        // Use a small additional delay for smoother experience
        Future.delayed(const Duration(milliseconds: 50), () {
          if (mounted && _scrollController.hasClients) {
            _scrollController.animateTo(
              _scrollController.position.maxScrollExtent,
              // Longer duration and different curve for smoother animation
              duration: const Duration(milliseconds: 500),
              curve: Curves.easeOutCubic,
            );
          }
        });
      }
    });
  }

  // Method to activate chat with smooth animation
  void _activateChat() {
    if (_chatActivated) return; // Prevent multiple activations

    setState(() {
      _chatActivated = true;
    });

    // Add initial AI message
    _addAssistantMessage(
        'Hello! I\'m Medroid, your personal AI Doctor. How can I help you today?');

    // Start animation with a smooth transition
    _animationController.forward().then((_) {
      // Use the improved scroll method
      _scrollToBottom();

      // Set focus to input field after animation completes
      if (mounted && !_isDisposing) {
        _inputFocusNode.requestFocus();
      }
    });
  }

  // Helper method to build minimal feature items with different colors
  Widget _buildMinimalFeatureItem(String text, Color accentColor) {
    return Row(
      children: [
        Container(
          width: 4,
          height: 20,
          decoration: BoxDecoration(
            color: accentColor,
            borderRadius: BorderRadius.circular(2),
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: Text(
            text,
            style: GoogleFonts.inter(
              fontSize: 16,
              color: AppColors.midnightNavy,
              fontWeight: FontWeight.w500,
              height: 1.4,
            ),
          ),
        ),
      ],
    );
  }

  // Helper method to build feature items for desktop view (legacy)
  Widget _buildFeatureItem(String text) {
    return Container(
      margin: const EdgeInsets.only(bottom: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            padding: const EdgeInsets.all(6),
            decoration: BoxDecoration(
              gradient: AppColors.getPrimaryGradient(),
              borderRadius: BorderRadius.circular(12),
              boxShadow: [
                BoxShadow(
                  color: AppColors.tealSurge.withAlpha(40),
                  blurRadius: 8,
                  spreadRadius: 0,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: const Icon(
              Icons.check_rounded,
              size: 16,
              color: Colors.white,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Padding(
              padding: const EdgeInsets.only(top: 2),
              child: Text(
                text,
                style: GoogleFonts.inter(
                  fontSize: 16,
                  color: const Color(0xFF2D3748),
                  fontWeight: FontWeight.w600,
                  height: 1.4,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Helper method to build the chat body
  Widget _buildChatBody() {
    // Use Column layout like authenticated chat for consistency and robustness
    return Column(
      children: [
        // Main Chat Area
        Expanded(
          child: _messages.isEmpty
              ? _buildWelcomeContent()
              : AnimatedBuilder(
                  animation: _animationController,
                  builder: (context, child) {
                    return FadeTransition(
                      opacity: _fadeAnimation,
                      child: Center(
                        child: Container(
                          constraints: BoxConstraints(
                            maxWidth: ResponsiveUtils.isDesktop(context)
                                ? 900 // Match desktop chat input width
                                : ResponsiveUtils.isTablet(context)
                                    ? 700 // Match tablet chat input width
                                    : double.infinity, // Full width for mobile
                          ),
                          child: ListView.builder(
                            controller: _scrollController,
                            padding: const EdgeInsets.fromLTRB(16, 8, 16, 90),
                            itemCount: _messages.length +
                                (_isTyping ? 1 : 0) +
                                1, // +1 for bottom padding
                            itemBuilder: (context, index) {
                              // Add extra bottom padding at the end of the list
                              if (index ==
                                  _messages.length + (_isTyping ? 1 : 0)) {
                                return const SizedBox(height: 40);
                              }

                              if (index == _messages.length) {
                                // Show typing indicator
                                return ChatMessageWidget(
                                  message: ChatMessage(
                                    id: 'typing',
                                    role: 'assistant',
                                    content: 'Typing...',
                                    timestamp: DateTime.now(),
                                    contentType: MessageContentType.text,
                                  ),
                                  isTyping: true,
                                );
                              }

                              return Padding(
                                padding: EdgeInsets.only(
                                  // Add extra top padding for the first message
                                  top: index == 0 ? 8 : 0,
                                  // Add extra bottom padding for the last message
                                  bottom: index == _messages.length - 1 ? 8 : 0,
                                ),
                                child: ChatMessageWidget(
                                  message: _messages[index],
                                  isTyping: false,
                                ),
                              );
                            },
                          ),
                        ),
                      ),
                    );
                  },
                ),
        ),

        // Chat Input - positioned below the expanded area like authenticated chat
        if (_messages.isNotEmpty)
          AnimatedOpacity(
            opacity: 1.0,
            duration: const Duration(milliseconds: 500),
            curve: Curves.easeInOut,
            child: UniversalChatInput(
              onSendMessage: (message, {File? imageFile}) => _sendMessage(
                message,
                imageFile: imageFile,
              ),
              conversationId: _conversationId,
              hintText: 'Type your health question...',
              isEnabled: !_isLoading && !_isTyping,
              focusNode: _inputFocusNode,
            ),
          ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Scaffold(
        body: Center(
          child: CircularProgressIndicator(),
        ),
      );
    }

    if (_hasError) {
      return Scaffold(
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Text('Failed to initialize chat'),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: _initializeChat,
                child: const Text('Retry'),
              ),
            ],
          ),
        ),
      );
    }

    // Check if we're on a desktop-sized screen
    final isDesktop = ResponsiveUtils.isDesktop(context);

    // For web, use a completely redesigned full-screen layout
    if (kIsWeb) {
      // Get screen dimensions for responsive design
      final screenWidth = MediaQuery.of(context).size.width;
      final isLargeScreen = screenWidth > 1200;
      final isMediumScreen = screenWidth > 900 && screenWidth <= 1200;

      return Scaffold(
        backgroundColor: Colors.white,
        body: SizedBox(
          width: double.infinity,
          height: double.infinity,
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              // Left panel with logo and info - responsive width
              if (isDesktop)
                Container(
                  width: isLargeScreen ? 420 : (isMediumScreen ? 360 : 320),
                  decoration: BoxDecoration(
                    color: AppColors.cloudWhite,
                    border: Border(
                      right: BorderSide(
                        color: AppColors.slateGrey.withAlpha(30),
                        width: 1,
                      ),
                    ),
                  ),
                  child: Container(
                    padding: EdgeInsets.symmetric(
                      horizontal: isLargeScreen ? 48 : 40,
                      vertical: 40,
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Logo and title - minimalistic
                        Row(
                          children: [
                            Image.asset(
                              'assets/images/medroid_icon.png',
                              height: 28,
                              width: 28,
                            ),
                            const SizedBox(width: 10),
                            Text(
                              'Medroid',
                              style: GoogleFonts.inter(
                                fontSize: 20,
                                fontWeight: FontWeight.w600,
                                color: AppColors.midnightNavy,
                                letterSpacing: -0.2,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 60),

                        // Main heading - elegant and clean
                        RichText(
                          text: TextSpan(
                            style: GoogleFonts.inter(
                              fontSize: isLargeScreen ? 40 : 36,
                              fontWeight: FontWeight.w700,
                              height: 1.2,
                              letterSpacing: -0.8,
                            ),
                            children: [
                              TextSpan(
                                text: 'Your free personal\n',
                                style: TextStyle(color: AppColors.midnightNavy),
                              ),
                              TextSpan(
                                text: 'AI Doctor ',
                                style: TextStyle(color: AppColors.tealSurge),
                              ),
                              TextSpan(
                                text: 'awaits you.',
                                style: TextStyle(color: AppColors.midnightNavy),
                              ),
                            ],
                          ),
                        ),
                        const SizedBox(height: 32),

                        // Subtitle with secondary color
                        Text(
                          'Fast, free, and private medical consultations powered by AI.',
                          style: GoogleFonts.inter(
                            fontSize: 16,
                            fontWeight: FontWeight.w400,
                            color: AppColors.slateGrey,
                            height: 1.5,
                          ),
                        ),
                        const SizedBox(height: 40),

                        // Features with different accent colors
                        _buildMinimalFeatureItem(
                          '100% free and private',
                          AppColors.tealSurge,
                        ),
                        const SizedBox(height: 16),
                        _buildMinimalFeatureItem(
                          'Instant medical advice',
                          AppColors.coralPop,
                        ),
                        const SizedBox(height: 16),
                        _buildMinimalFeatureItem(
                          'Book real doctor appointments',
                          AppColors.mintGlow,
                        ),

                        const Spacer(),

                        // Sleek sign in button
                        Material(
                          color: Colors.transparent,
                          child: InkWell(
                            onTap: () => _navigateToAuth(
                                reason: 'Sign in to access all features'),
                            borderRadius: BorderRadius.circular(8),
                            child: Container(
                              padding: const EdgeInsets.symmetric(vertical: 12),
                              decoration: BoxDecoration(
                                color: AppColors.midnightNavy,
                                borderRadius: BorderRadius.circular(8),
                              ),
                              child: Center(
                                child: Text(
                                  'Sign In',
                                  style: GoogleFonts.inter(
                                    fontSize: 15,
                                    fontWeight: FontWeight.w600,
                                    color: Colors.white,
                                    letterSpacing: 0.3,
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),

              // Main chat area - takes remaining width
              Expanded(
                child: Container(
                  decoration: isDesktop
                      ? BoxDecoration(
                          color: AppColors.backgroundLight,
                          boxShadow: [
                            BoxShadow(
                              color: AppColors.slateGrey.withAlpha(15),
                              blurRadius: 1,
                              spreadRadius: 0,
                              offset: const Offset(-1, 0),
                            ),
                          ],
                        )
                      : null,
                  child: Scaffold(
                    backgroundColor: AppColors.backgroundLight,
                    appBar: AppBar(
                      backgroundColor: Colors.transparent,
                      elevation: 0,
                      foregroundColor: AppColors.midnightNavy,
                      title: _messages.isEmpty
                          ? null
                          : Row(
                              mainAxisAlignment: MainAxisAlignment.start,
                              children: [
                                Image.asset(
                                  'assets/images/medroid_icon.png',
                                  height: 24,
                                  width: 24,
                                ),
                                const SizedBox(width: 10),
                                Text(
                                  'Medroid',
                                  style: GoogleFonts.inter(
                                    fontSize: 18,
                                    fontWeight: FontWeight.w600,
                                    color: AppColors.midnightNavy,
                                    letterSpacing: -0.2,
                                  ),
                                ),
                              ],
                            ),
                      titleSpacing: 20.0,
                      actions: [
                        if (_messages.isNotEmpty)
                          Padding(
                            padding: const EdgeInsets.only(right: 8.0),
                            child: Material(
                              color: Colors.transparent,
                              child: InkWell(
                                borderRadius: BorderRadius.circular(8),
                                onTap: _startNewChat,
                                child: Container(
                                  padding: const EdgeInsets.all(8.0),
                                  child: Icon(
                                    Icons.refresh_outlined,
                                    size: 20,
                                    color: AppColors.slateGrey,
                                  ),
                                ),
                              ),
                            ),
                          ),
                        Padding(
                          padding: const EdgeInsets.only(right: 20.0),
                          child: Material(
                            color: Colors.transparent,
                            child: InkWell(
                              borderRadius: BorderRadius.circular(6),
                              onTap: () {
                                _navigateToAuth(
                                    reason: 'Sign in to access all features');
                              },
                              child: Container(
                                padding: const EdgeInsets.all(8.0),
                                child: Icon(
                                  Icons.login_outlined,
                                  size: 20,
                                  color: AppColors.slateGrey,
                                ),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                    body: _buildChatBody(),
                    // Only show bottom navigation on mobile, not on desktop or tablet
                    bottomNavigationBar: !isDesktop
                        ? CustomBottomNavigation(
                            currentIndex: 0, // Always show chat tab as selected
                            isAnonymousMode: true,
                            onTap: (index) {
                              // Only allow tapping on the chat tab (index 0)
                              if (index != 0) {
                                _showAuthPrompt(
                                    reason:
                                        'Please sign in to access this feature');
                              } else {
                                // If tapping on the chat tab, ensure focus is on the input field
                                if (!_isDisposing && _messages.isNotEmpty) {
                                  _inputFocusNode.requestFocus();
                                }
                              }
                            },
                            items: [
                              // Same items as in MainNavigation for consistency
                              CustomNavItem(
                                icon: Icons.chat_bubble_outline_rounded,
                                activeIcon: Icons.chat_bubble_rounded,
                                label: 'Chat',
                              ),
                              CustomNavItem(
                                icon: Icons.explore_outlined,
                                activeIcon: Icons.explore,
                                label: 'Discover',
                              ),
                              CustomNavItem(
                                icon: Icons.history_rounded,
                                activeIcon: Icons.history_rounded,
                                label: 'History',
                              ),
                              CustomNavItem(
                                icon: Icons.shopping_bag_outlined,
                                activeIcon: Icons.shopping_bag,
                                label: 'Shop',
                              ),
                              CustomNavItem(
                                icon: Icons.person_outline_rounded,
                                activeIcon: Icons.person_rounded,
                                label: 'Profile',
                              ),
                            ],
                          )
                        : null,
                  ),
                ),
              ),
            ],
          ),
        ),
      );
    }

    // Mobile layout (original layout)
    return Scaffold(
      backgroundColor: AppColors.backgroundLight,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0, // Remove shadow
        foregroundColor: AppColors.midnightNavy,
        // Custom title bar with left-aligned logo and text
        title: _messages.isEmpty
            ? null
            : Row(
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  Image.asset(
                    'assets/images/medroid_icon.png',
                    height: 22,
                    width: 22,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    'Medroid',
                    style: GoogleFonts.inter(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: AppColors.midnightNavy,
                      letterSpacing: -0.1,
                    ),
                  ),
                ],
              ),
        // No leading widget when using custom title
        leading: null,
        // Add left padding to align with messages
        titleSpacing: 16.0,
        actions: [
          // Only show the new chat button if there are messages
          if (_messages.isNotEmpty)
            Padding(
              padding: const EdgeInsets.only(right: 8.0),
              child: Material(
                color: Colors.transparent,
                child: InkWell(
                  borderRadius: BorderRadius.circular(6),
                  onTap: _startNewChat,
                  child: Container(
                    padding: const EdgeInsets.all(6.0),
                    child: Icon(
                      Icons.refresh_outlined,
                      size: 18,
                      color: AppColors.slateGrey,
                    ),
                  ),
                ),
              ),
            ),
          // Login button
          Padding(
            padding: const EdgeInsets.only(right: 16.0),
            child: Material(
              color: Colors.transparent,
              child: InkWell(
                borderRadius: BorderRadius.circular(6),
                onTap: () {
                  _navigateToAuth(reason: 'Sign in to access all features');
                },
                child: Container(
                  padding: const EdgeInsets.all(6.0),
                  child: Icon(
                    Icons.login_outlined,
                    size: 18,
                    color: AppColors.slateGrey,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
      body: _buildChatBody(),
      // Only show bottom navigation on mobile, not on desktop or tablet
      bottomNavigationBar: !ResponsiveUtils.isDesktop(context) &&
              !ResponsiveUtils.isTablet(context)
          ? CustomBottomNavigation(
              currentIndex: 0, // Always show chat tab as selected
              isAnonymousMode: true,
              onTap: (index) {
                // Only allow tapping on the chat tab (index 0)
                if (index != 0) {
                  _showAuthPrompt(
                      reason: 'Please sign in to access this feature');
                } else {
                  // If tapping on the chat tab, ensure focus is on the input field
                  if (!_isDisposing && _messages.isNotEmpty) {
                    _inputFocusNode.requestFocus();
                  }
                }
              },
              items: [
                // Same items as in MainNavigation for consistency
                CustomNavItem(
                  icon: Icons.chat_bubble_outline_rounded,
                  activeIcon: Icons.chat_bubble_rounded,
                  label: 'Chat',
                ),
                CustomNavItem(
                  icon: Icons.explore_outlined,
                  activeIcon: Icons.explore,
                  label: 'Discover',
                ),
                CustomNavItem(
                  icon: Icons.history_rounded,
                  activeIcon: Icons.history_rounded,
                  label: 'History',
                ),
                CustomNavItem(
                  icon: Icons.shopping_bag_outlined,
                  activeIcon: Icons.shopping_bag,
                  label: 'Shop',
                ),
                CustomNavItem(
                  icon: Icons.person_outline_rounded,
                  activeIcon: Icons.person_rounded,
                  label: 'Profile',
                ),
              ],
            )
          : null,
    );
  }

  // Build the welcome content that appears in the chat area
  Widget _buildWelcomeContent() {
    // For web, we need to check if we're in desktop mode to avoid duplication
    final bool isWebDesktop = kIsWeb && ResponsiveUtils.isDesktop(context);
    final bool isWebMobile = kIsWeb && !ResponsiveUtils.isDesktop(context);
    final bool isMobile = !kIsWeb &&
        !ResponsiveUtils.isDesktop(context) &&
        !ResponsiveUtils.isTablet(context);

    // For mobile, use a fixed height container with fixed position input bar
    if (isMobile) {
      return Stack(
        children: [
          // Main content in a non-scrollable container
          Container(
            height: MediaQuery.of(context).size.height -
                AppBar().preferredSize.height -
                MediaQuery.of(context).padding.top -
                MediaQuery.of(context).padding.bottom -
                80, // Reserve space for the input bar
            padding: const EdgeInsets.symmetric(horizontal: 24.0),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Spacer(flex: 1),

                // Logo and title - minimalistic
                Center(
                  child: Column(
                    children: [
                      Image.asset(
                        'assets/images/medroid_icon.png',
                        height: 56,
                        width: 56,
                      ),
                      const SizedBox(height: 16),
                      Text(
                        'Medroid',
                        style: GoogleFonts.inter(
                          fontSize: 32,
                          fontWeight: FontWeight.w700,
                          color: AppColors.midnightNavy,
                          letterSpacing: -0.5,
                        ),
                      ),
                    ],
                  ),
                ),

                const SizedBox(height: 40),

                // Main heading - clean and elegant
                Center(
                  child: RichText(
                    textAlign: TextAlign.center,
                    text: TextSpan(
                      style: GoogleFonts.inter(
                        fontSize: 30,
                        fontWeight: FontWeight.w700,
                        height: 1.3,
                        letterSpacing: -0.5,
                      ),
                      children: [
                        TextSpan(
                          text: 'Your free personal\n',
                          style: TextStyle(color: AppColors.midnightNavy),
                        ),
                        TextSpan(
                          text: 'AI Doctor ',
                          style: TextStyle(color: AppColors.tealSurge),
                        ),
                        TextSpan(
                          text: 'awaits you.',
                          style: TextStyle(color: AppColors.midnightNavy),
                        ),
                      ],
                    ),
                  ),
                ),

                const SizedBox(height: 40),

                // Elegant divider
                _buildSectionDivider(),

                const SizedBox(height: 40),

                // AI introduction messages
                Container(
                  constraints: const BoxConstraints(maxWidth: 500),
                  child: Column(
                    children: [
                      _buildIntroMessageBubble(
                        'Hi, I\'m Medroid, your personal AI doctor.',
                        isFirst: true,
                      ),
                      const SizedBox(height: 16),
                      _buildIntroMessageBubble(
                        'As an AI doctor, I\'m fast and free. I\'ve already conducted 1,000,000+ consultations!',
                      ),
                      const SizedBox(height: 16),
                      _buildIntroMessageBubble(
                        'When you\'re done, you can have a video consultation with a world class doctor, if you want, for just £55.',
                      ),
                    ],
                  ),
                ),

                const Spacer(flex: 1),
              ],
            ),
          ),

          // Fixed position chat input at the bottom
          Positioned(
            bottom: 0,
            left: 0,
            right: 0,
            child: Container(
              padding:
                  const EdgeInsets.symmetric(horizontal: 16.0, vertical: 12.0),
              decoration: BoxDecoration(
                color: AppColors.cloudWhite,
                border: Border(
                  top: BorderSide(
                    color: AppColors.slateGrey.withAlpha(20),
                    width: 1,
                  ),
                ),
              ),
              child: UniversalChatInput(
                onSendMessage: (message, {File? imageFile}) {
                  // First activate the chat
                  _activateChat();

                  // Then send the message after a short delay to allow animation to complete
                  Future.delayed(const Duration(milliseconds: 300), () {
                    if (mounted) {
                      _sendMessage(message, imageFile: imageFile);
                    }
                  });
                },
                hintText: 'Type your health question...',
                isEnabled: true,
                focusNode: _inputFocusNode,
              ),
            ),
          ),
        ],
      );
    }

    // For web and tablet, use the scrollable version
    // Note: Don't use _scrollController here to avoid conflicts with chat messages ListView
    return ListView(
      padding: EdgeInsets.symmetric(
          horizontal: isWebMobile ? 32.0 : 24.0, vertical: 32.0),
      children: [
        const SizedBox(height: 20),

        // Logo and title - only show on tablet web
        if (!isWebDesktop)
          Center(
            child: Column(
              children: [
                Image.asset(
                  'assets/images/medroid_icon.png',
                  height: 64,
                  width: 64,
                ),
                const SizedBox(height: 20),
                Text(
                  'Medroid',
                  style: GoogleFonts.inter(
                    fontSize: 36,
                    fontWeight: FontWeight.w700,
                    color: AppColors.midnightNavy,
                    letterSpacing: -0.7,
                  ),
                ),
              ],
            ),
          ),

        if (!isWebDesktop) const SizedBox(height: 40),

        // Main heading - only show on tablet web
        if (!isWebDesktop)
          Center(
            child: RichText(
              textAlign: TextAlign.center,
              text: TextSpan(
                style: GoogleFonts.inter(
                  fontSize: 34,
                  fontWeight: FontWeight.w700,
                  height: 1.3,
                  letterSpacing: -0.6,
                ),
                children: [
                  TextSpan(
                    text: 'Your free personal\n',
                    style: TextStyle(color: AppColors.midnightNavy),
                  ),
                  TextSpan(
                    text: 'AI Doctor ',
                    style: TextStyle(color: AppColors.tealSurge),
                  ),
                  TextSpan(
                    text: 'awaits you.',
                    style: TextStyle(color: AppColors.midnightNavy),
                  ),
                ],
              ),
            ),
          ),

        if (!isWebDesktop) const SizedBox(height: 32),

        // Features list - only show on tablet web (not on mobile)
        if (!isWebDesktop && !isMobile && ResponsiveUtils.isTablet(context))
          Center(
            child: Column(
              children: [
                _buildCenteredFeatureItem('100% free and private'),
                const SizedBox(height: 12),
                _buildCenteredFeatureItem('Instant medical advice'),
                const SizedBox(height: 12),
                _buildCenteredFeatureItem(
                    'Book appointments with real doctors'),
              ],
            ),
          ),

        // Always show a spacer
        SizedBox(height: isWebDesktop ? 20 : 40),

        // Elegant divider
        _buildSectionDivider(),

        SizedBox(height: isWebDesktop ? 20 : 40),

        // AI introduction messages - always show
        Center(
          child: Container(
            constraints: BoxConstraints(maxWidth: isWebMobile ? 600 : 500),
            child: Column(
              children: [
                _buildIntroMessageBubble(
                  'Hi, I\'m Medroid, your personal AI doctor.',
                  isFirst: true,
                ),
                const SizedBox(height: 16),
                _buildIntroMessageBubble(
                  'As an AI doctor, I\'m fast and free. I\'ve already conducted 1,000,000+ consultations!',
                ),
                const SizedBox(height: 16),
                _buildIntroMessageBubble(
                  'When you\'re done, you can have a video consultation with a world class doctor, if you want, for just £55.',
                ),
              ],
            ),
          ),
        ),

        const SizedBox(height: 40),

        // Chat input bar instead of Get Started button
        Container(
          // Use same width constraints as the chat messages
          constraints: BoxConstraints(
            maxWidth: ResponsiveUtils.isDesktop(context)
                ? 900 // Match desktop chat input width
                : ResponsiveUtils.isTablet(context)
                    ? 700 // Match tablet chat input width
                    : double.infinity, // Full width for mobile
          ),
          child: UniversalChatInput(
            onSendMessage: (message, {File? imageFile}) {
              // First activate the chat
              _activateChat();

              // Then send the message after a short delay to allow animation to complete
              Future.delayed(const Duration(milliseconds: 300), () {
                if (mounted) {
                  _sendMessage(message, imageFile: imageFile);
                }
              });
            },
            hintText: 'Type your health question...',
            isEnabled: true,
            focusNode: _inputFocusNode,
          ),
        ),
        const SizedBox(height: 20),
      ],
    );
  }

  // Helper method to build centered feature items for mobile/tablet web
  Widget _buildCenteredFeatureItem(String text) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: const Color(0xFFE6F7F5),
          width: 1,
        ),
        // Subtle elegant shadow
        boxShadow: const [
          BoxShadow(
            color: Color(0x05000000), // 2% opacity
            blurRadius: 4,
            spreadRadius: 0,
            offset: Offset(0, 1),
          ),
        ],
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            padding: const EdgeInsets.all(4),
            decoration: BoxDecoration(
              color: AppColors.tealSurge.withAlpha(15),
              shape: BoxShape.circle,
            ),
            child: const Icon(
              Icons.check,
              size: 16,
              color: AppColors.tealSurge,
            ),
          ),
          const SizedBox(width: 12),
          Text(
            text,
            style: GoogleFonts.inter(
              fontSize: 16,
              color: Colors.grey.shade700,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  // Helper method to build elegant section divider
  Widget _buildSectionDivider() {
    return Center(
      child: Container(
        width: 60,
        height: 3,
        decoration: BoxDecoration(
          color: AppColors.tealSurge,
          borderRadius: BorderRadius.circular(2),
        ),
      ),
    );
  }

  // Helper method to build intro message bubbles
  Widget _buildIntroMessageBubble(String message, {bool isFirst = false}) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: isFirst ? AppColors.cloudWhite : Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: isFirst
              ? AppColors.tealSurge.withAlpha(30)
              : AppColors.slateGrey.withAlpha(20),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: AppColors.slateGrey.withAlpha(8),
            blurRadius: 8,
            spreadRadius: 0,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (isFirst)
            Container(
              margin: const EdgeInsets.only(right: 12, top: 1),
              child: Icon(
                Icons.medical_services_rounded,
                size: 18,
                color: AppColors.tealSurge,
              ),
            ),
          Expanded(
            child: Text(
              message,
              style: GoogleFonts.inter(
                fontSize: 15,
                color: AppColors.midnightNavy,
                height: 1.5,
                fontWeight: isFirst ? FontWeight.w600 : FontWeight.w400,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
