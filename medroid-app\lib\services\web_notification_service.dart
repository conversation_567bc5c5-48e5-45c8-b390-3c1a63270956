/// Web notification service for handling notifications in web environment
/// This is a no-op implementation for mobile platforms
class WebNotificationService {
  /// Initialize web notifications (no-op for mobile)
  Future<void> initialize() async {
    // No-op for mobile platforms
  }

  /// Request notification permissions (no-op for mobile)
  Future<bool> requestPermission() async {
    return false;
  }

  /// Show a notification (no-op for mobile)
  Future<void> showNotification({
    required String title,
    required String body,
    String? icon,
    Map<String, dynamic>? data,
  }) async {
    // No-op for mobile platforms
  }

  /// Get notification count (always 0 for mobile)
  Future<int> getNotificationCount() async {
    return 0;
  }

  /// Clear all notifications (no-op for mobile)
  Future<void> clearAllNotifications() async {
    // No-op for mobile platforms
  }

  /// Handle notification click (no-op for mobile)
  void handleNotificationClick(Map<String, dynamic> data) {
    // No-op for mobile platforms
  }

  /// Dispose resources (no-op for mobile)
  void dispose() {
    // No-op for mobile platforms
  }
}
