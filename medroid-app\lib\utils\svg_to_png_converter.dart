import 'dart:io';
import 'dart:ui' as ui;
import 'package:flutter/services.dart';
import 'package:flutter_svg/flutter_svg.dart';

// This is a utility class to convert SVG to PNG at runtime
// We'll use this for development purposes
class SvgToPngConverter {
  static Future<void> convertAndSave() async {
    // List of SVG files to convert
    final svgFiles = [
      'assets/icons/medroid.svg',
      'assets/icons/instagram.svg',
      'assets/icons/tiktok.svg',
      'assets/icons/social.svg',
    ];

    for (final svgPath in svgFiles) {
      final pngPath = svgPath.replaceAll('.svg', '.png');

      // Load the SVG file
      final svgString = await rootBundle.loadString(svgPath);

      // Create a DrawableRoot from the SVG string
      final pictureInfo =
          await vg.loadPicture(SvgStringLoader(svgString), null);

      // Convert to an Image
      final image = await pictureInfo.picture.toImage(
        pictureInfo.size.width.toInt(),
        pictureInfo.size.height.toInt(),
      );

      // Convert to bytes
      final byteData = await image.toByteData(format: ui.ImageByteFormat.png);
      final buffer = byteData!.buffer.asUint8List();

      // Save to file
      final file = File(pngPath);
      await file.writeAsBytes(buffer);
    }
  }
}
