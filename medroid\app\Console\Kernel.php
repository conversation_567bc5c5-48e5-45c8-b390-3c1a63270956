<?php

namespace App\Console;

use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;

class Kernel extends ConsoleKernel
{
    /**
     * The Artisan commands provided by your application.
     *
     * @var array
     */
    protected $commands = [
        //
    ];

    /**
     * Define the application's command schedule.
     *
     * @param  \Illuminate\Console\Scheduling\Schedule  $schedule
     * @return void
     */
    protected function schedule(Schedule $schedule)
    {
        // Run MongoDB health check every 5 minutes
        $schedule->command('mongodb:health-check --silent')
            ->everyFiveMinutes()
            ->appendOutputTo(storage_path('logs/mongodb-health.log'));

        // Run MongoDB health check with detailed output once per day
        $schedule->command('mongodb:health-check')
            ->dailyAt('01:00')
            ->appendOutputTo(storage_path('logs/mongodb-health-daily.log'));

        // Run MongoDB backup daily
        $schedule->command('mongodb:backup --disk=s3 --path=mongodb-backups')
            ->dailyAt('02:00')
            ->appendOutputTo(storage_path('logs/mongodb-backup.log'))
            ->emailOutputOnFailure(env('ADMIN_EMAIL'));

        // Run MongoDB backup weekly with local storage
        $schedule->command('mongodb:backup')
            ->weekly()
            ->appendOutputTo(storage_path('logs/mongodb-backup-weekly.log'))
            ->emailOutputOnFailure(env('ADMIN_EMAIL'));

        // Send appointment reminders daily at 10:00 AM
        $schedule->command('appointments:send-reminders')
            ->dailyAt('10:00')
            ->appendOutputTo(storage_path('logs/appointment-reminders.log'))
            ->emailOutputOnFailure(env('ADMIN_EMAIL'));

        // Process scheduled notifications every minute
        $schedule->command('notifications:process-scheduled')
            ->everyMinute()
            ->appendOutputTo(storage_path('logs/scheduled-notifications.log'))
            ->emailOutputOnFailure(env('ADMIN_EMAIL'));

        // Update referral activity tracking daily
        $schedule->command('referrals:update-activity')
            ->dailyAt('00:00')
            ->appendOutputTo(storage_path('logs/referral-activity.log'))
            ->emailOutputOnFailure(env('ADMIN_EMAIL'));

        // Process pending referral credits daily (after activity tracking)
        $schedule->command('referrals:process-credits')
            ->dailyAt('00:30')
            ->appendOutputTo(storage_path('logs/referral-credits.log'))
            ->emailOutputOnFailure(env('ADMIN_EMAIL'));

        // Award referral credits for users with 3+ days activity
        $schedule->command('referrals:award-credits')
            ->dailyAt('01:00')
            ->appendOutputTo(storage_path('logs/referral-credit-awards.log'))
            ->emailOutputOnFailure(env('ADMIN_EMAIL'));

        // Clean up video sessions every 10 minutes
        $schedule->command('video:cleanup-sessions')
            ->everyTenMinutes()
            ->appendOutputTo(storage_path('logs/video-cleanup.log'))
            ->emailOutputOnFailure(env('ADMIN_EMAIL'));

        // Sync Instagram content every 15 minutes for real-time updates
        $schedule->command('instagram:sync')
            ->everyFifteenMinutes()
            ->appendOutputTo(storage_path('logs/instagram-sync.log'))
            ->emailOutputOnFailure(env('ADMIN_EMAIL'));

        // Force sync Instagram content daily to ensure all content is captured
        $schedule->command('instagram:sync --force')
            ->dailyAt('03:00')
            ->appendOutputTo(storage_path('logs/instagram-sync-daily.log'))
            ->emailOutputOnFailure(env('ADMIN_EMAIL'));
    }

    /**
     * Register the commands for the application.
     *
     * @return void
     */
    protected function commands()
    {
        $this->load(__DIR__.'/Commands');

        require base_path('routes/console.php');
    }
}
