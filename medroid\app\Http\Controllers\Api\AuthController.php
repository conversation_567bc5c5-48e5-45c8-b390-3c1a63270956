<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Services\AuthService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;

class AuthController extends Controller
{
    protected $authService;

    public function __construct(AuthService $authService)
    {
        $this->authService = $authService;
    }

    /**
     * Register a new user
     * Delegates to the main AuthController for consistency
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function register(Request $request)
    {
        // Use the main AuthController's register method for consistency
        $authController = app(\App\Http\Controllers\AuthController::class);
        $response = $authController->register($request);

        // Return the response as JSON for API consumption
        return $response;
    }

    /**
     * Login user and return token
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function login(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'email' => 'required|email',
            'password' => 'required|string',
            'device_name' => 'nullable|string',
            'device_type' => 'nullable|string|in:android,ios,web',
            'user_agent' => 'nullable|string',
            'device_model' => 'nullable|string',
            'platform' => 'nullable|string',
            'app_version' => 'nullable|string',
            'device_token' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        // Find the user by email
        $user = User::where('email', $request->email)->first();

        // Check if user exists and password is correct
        if (!$user || !Hash::check($request->password, $user->password)) {
            return response()->json([
                'message' => 'Invalid credentials',
            ], 401);
        }

        // Update last login timestamp
        $user->last_login_at = now();
        $user->save();

        // Create token that doesn't expire (for better Flutter compatibility)
        $deviceName = $request->device_name ?? 'flutter-app';
        $tokenResult = $user->createToken($deviceName);
        $token = $tokenResult->plainTextToken;

        // Store device token if provided
        if ($request->has('device_token') && !empty($request->device_token)) {
            try {
                app(\App\Services\NotificationService::class)->storeDeviceToken(
                    $user,
                    $request->device_token,
                    $request->device_type ?? 'android'
                );
            } catch (\Exception $e) {
                // Don't fail login if device token storage fails
                \Log::warning('Failed to store device token: ' . $e->getMessage());
            }
        }

        // Load user relationships
        $user->load(['roles']);
        if ($user->role === 'patient') {
            $user->load('patient');
        } elseif ($user->role === 'provider') {
            $user->load('provider');
        }

        return response()->json([
            'user' => $user,
            'access_token' => $token,
            'token_type' => 'Bearer',
        ]);
    }

    /**
     * Get authenticated user
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function user(Request $request)
    {
        $user = $request->user();

        if (!$user) {
            return response()->json(['message' => 'Unauthenticated'], 401);
        }

        $this->authService->loadUserRoleData($user);

        return response()->json($user);
    }

    /**
     * Logout user
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function logout(Request $request)
    {
        try {
            $user = $request->user();
            
            if (!$user) {
                return response()->json(['message' => 'User not authenticated'], 401);
            }

            // Get the current access token
            $token = $user->currentAccessToken();
            
            if ($token) {
                $token->delete();
                \Log::info('User logged out successfully', [
                    'user_id' => $user->id,
                    'token_id' => $token->id
                ]);
            } else {
                \Log::warning('No current access token found during logout', [
                    'user_id' => $user->id
                ]);
            }
            
            return response()->json(['message' => 'Logged out successfully']);
            
        } catch (\Exception $e) {
            \Log::error('Error during logout: ' . $e->getMessage(), [
                'exception' => $e,
                'user_id' => $request->user()->id ?? null,
            ]);
            
            return response()->json([
                'message' => 'Logout completed',
                'note' => 'Session may have already been terminated'
            ]);
        }
    }

    /**
     * Handle SSO login/registration - delegates to main AuthController
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function ssoLogin(Request $request)
    {
        // Use the main AuthController's ssoLogin method for consistency
        $authController = app(\App\Http\Controllers\AuthController::class);
        return $authController->ssoLogin($request);
    }

    /**
     * Handle SSO provider registration - delegates to main AuthController
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function ssoProviderRegistration(Request $request)
    {
        // Use the main AuthController's ssoProviderRegistration method for consistency
        $authController = app(\App\Http\Controllers\AuthController::class);
        return $authController->ssoProviderRegistration($request);
    }

    /**
     * Complete SSO registration - delegates to main AuthController
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function ssoCompleteRegistration(Request $request)
    {
        // Use the main AuthController's ssoCompleteRegistration method for consistency
        $authController = app(\App\Http\Controllers\AuthController::class);
        return $authController->ssoCompleteRegistration($request);
    }
}