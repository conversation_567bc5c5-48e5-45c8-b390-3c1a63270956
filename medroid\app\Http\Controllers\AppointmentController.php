<?php

namespace App\Http\Controllers;

use App\Models\Appointment;
use App\Models\Patient;
use App\Models\Provider;
use App\Models\Service;
use App\Notifications\AppointmentBookedNotification;
use App\Notifications\AppointmentCancelledNotification;
use App\Notifications\AppointmentRescheduledNotification;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;
use Inertia\Inertia;
use Inertia\Response;
use App\Services\AppointmentService;

class AppointmentController extends Controller
{
    protected $appointmentService;

    public function __construct(AppointmentService $appointmentService)
    {
        $this->appointmentService = $appointmentService;
    }
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'provider_id' => 'required|exists:providers,id',
            'service_id' => 'nullable|exists:services,id',
            'date' => 'required|date|after_or_equal:today',
            'time_slot.start_time' => 'required|string',
            'time_slot.end_time' => 'required|string',
            'reason' => 'required|string',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $user = $request->user();
        $patient = Patient::where('user_id', $user->id)->first();

        if (!$patient) {
            return response()->json([
                'message' => 'Patient profile not found'
            ], 404);
        }

        $provider = Provider::findOrFail($request->provider_id);

        // Check if the provider is available at the requested time
        $isAvailable = $provider->isAvailable(
            $request->date,
            $request->time_slot
        );

        if (!$isAvailable) {
            return response()->json([
                'message' => 'Provider is not available at the requested time'
            ], 400);
        }

        \Log::info('Provider ' . $provider->id . ' is available at the requested time: ' .
            $request->date . ' ' . json_encode($request->time_slot));

        // Create appointment data
        $appointmentData = [
            'patient_id' => $patient->id,
            'provider_id' => $provider->id,
            'date' => $request->date,
            'time_slot' => $request->time_slot,
            'reason' => $request->reason,
            'status' => 'scheduled',
            'notes' => $request->notes ?? '',
        ];

        // Add service_id if provided
        if ($request->has('service_id')) {
            // Verify the service belongs to the provider
            $service = $provider->services()->findOrFail($request->service_id);
            $appointmentData['service_id'] = $service->id;

            // Check if this is a telemedicine service
            if ($service->is_telemedicine) {
                $appointmentData['is_telemedicine'] = true;
            }

            // Set payment information
            $appointmentData['amount'] = $service->price;
            $appointmentData['payment_status'] = 'unpaid';
            $appointmentData['payment_due_by'] = now()->addDays(1); // Payment due within 24 hours
        }

        $appointment = Appointment::create($appointmentData);

        // Send appointment booked notifications
        try {
            // Send notification to patient
            $patient->user->notify(new AppointmentBookedNotification($appointment, false));
            Log::info('Appointment booked notification sent to patient', [
                'appointment_id' => $appointment->id,
                'patient_id' => $patient->id,
                'email' => $patient->user->email
            ]);

            // Send notification to provider
            $provider->user->notify(new AppointmentBookedNotification($appointment, true));
            Log::info('Appointment booked notification sent to provider', [
                'appointment_id' => $appointment->id,
                'provider_id' => $provider->id,
                'email' => $provider->user->email
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to send appointment booked notification', [
                'appointment_id' => $appointment->id,
                'error' => $e->getMessage()
            ]);
        }

        // Prepare response data
        $responseData = [
            'message' => 'Appointment created successfully',
            'appointment' => $appointment
        ];

        // Add payment information if applicable
        if (isset($appointmentData['amount']) && $appointmentData['amount'] > 0) {
            $responseData['payment_required'] = true;
            $responseData['amount'] = $appointmentData['amount'];
            $responseData['payment_due_by'] = $appointmentData['payment_due_by'];
            $responseData['payment_instructions'] = 'Please complete payment to confirm your appointment.';
        }

        return response()->json($responseData, 201);
    }

    public function userAppointments(Request $request)
    {
        $user = $request->user();

        if ($user->role === 'patient') {
            $patient = Patient::where('user_id', $user->id)->first();

            if (!$patient) {
                return response()->json([
                    'message' => 'Patient profile not found'
                ], 404);
            }

            $appointments = Appointment::where('patient_id', $patient->id)
                ->with(['provider.user', 'patient.user', 'service'])
                ->orderBy('date', 'desc')
                ->get();

        } elseif ($user->role === 'provider') {
            $provider = Provider::where('user_id', $user->id)->first();

            if (!$provider) {
                return response()->json([
                    'message' => 'Provider profile not found'
                ], 404);
            }

            $appointments = Appointment::where('provider_id', $provider->id)
                ->with(['patient.user', 'provider.user', 'service'])
                ->orderBy('date', 'desc')
                ->get();

        } elseif (in_array($user->role, ['admin', 'manager']) || $user->can('view appointments')) {
            // Admin/manager users can see all appointments
            $appointments = Appointment::with(['patient.user', 'provider.user', 'service'])
                ->orderBy('date', 'desc')
                ->get();

        } else {
            return response()->json([
                'message' => 'Unauthorized'
            ], 403);
        }

        // Transform appointments to include required fields for frontend
        $transformedAppointments = $appointments->map(function ($appointment) {
            $transformed = $appointment->toArray();

            // Add time field from time_slot for compatibility
            if (isset($appointment->time_slot['start_time'])) {
                $transformed['time'] = $appointment->time_slot['start_time'];
            }

            // Ensure patient data is properly structured
            if ($appointment->patient && $appointment->patient->user) {
                $transformed['patient_name'] = $appointment->patient->user->name;
                $transformed['patient']['name'] = $appointment->patient->user->name;
                $transformed['patient']['email'] = $appointment->patient->user->email;
            } else {
                $transformed['patient_name'] = 'Unknown Patient';
            }

            // Ensure provider data is properly structured
            if ($appointment->provider && $appointment->provider->user) {
                $transformed['provider_name'] = $appointment->provider->user->name;
                $transformed['provider']['name'] = $appointment->provider->user->name;
                $transformed['provider']['specialization'] = $appointment->provider->specialization;
                $transformed['provider']['profile_image'] = $appointment->provider->profile_image;
            } else {
                $transformed['provider_name'] = 'Unknown Provider';
            }

            // Add default service if missing
            if (!$appointment->service) {
                $transformed['service'] = [
                    'name' => 'General Consultation',
                    'id' => null
                ];
            }

            return $transformed;
        });

        return response()->json($transformedAppointments);
    }

    /**
     * Get appointments for a specific patient
     *
     * @param Request $request
     * @param int $patientId
     * @return \Illuminate\Http\Response
     */
    public function patientAppointments(Request $request, $patientId)
    {
        $user = $request->user();

        // Only providers can access this endpoint
        if ($user->role !== 'provider') {
            return response()->json([
                'message' => 'Unauthorized'
            ], 403);
        }

        $provider = Provider::where('user_id', $user->id)->first();

        if (!$provider) {
            return response()->json([
                'message' => 'Provider profile not found'
            ], 404);
        }

        // Get all appointments for this patient with this provider
        $appointments = Appointment::where('patient_id', $patientId)
            ->where('provider_id', $provider->id)
            ->with('provider.user')
            ->orderBy('date', 'desc')
            ->get();

        return response()->json($appointments);
    }

    public function update(Request $request, $id)
    {
        $validator = Validator::make($request->all(), [
            'status' => 'sometimes|required|in:scheduled,completed,cancelled',
            'notes' => 'sometimes|required|string',
            'date' => 'sometimes|required|date|after_or_equal:today',
            'time_slot' => 'sometimes|required|array',
            'time_slot.start_time' => 'required_with:time_slot|string',
            'time_slot.end_time' => 'required_with:time_slot|string',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $user = $request->user();
        $appointment = Appointment::findOrFail($id);

        // Verify the user is authorized to update this appointment
        if ($user->role === 'patient') {
            $patient = Patient::where('user_id', $user->id)->first();

            if ($appointment->patient_id !== $patient->id) {
                return response()->json([
                    'message' => 'Unauthorized'
                ], 403);
            }

            // Patients can only cancel appointments or reschedule
            if ($request->has('status') && $request->status !== 'cancelled' && !$request->has('date')) {
                return response()->json([
                    'message' => 'Patients can only cancel or reschedule appointments'
                ], 403);
            }

        } elseif ($user->role === 'provider') {
            $provider = Provider::where('user_id', $user->id)->first();

            if ($appointment->provider_id !== $provider->id) {
                return response()->json([
                    'message' => 'Unauthorized'
                ], 403);
            }
        } else {
            return response()->json([
                'message' => 'Unauthorized'
            ], 403);
        }

        // Store original appointment data for notifications
        $oldDate = $appointment->date;
        $oldTimeSlot = $appointment->time_slot;
        $oldStatus = $appointment->status;

        // Check if this is a rescheduling request
        if ($request->has('date') && $request->has('time_slot')) {
            $provider = Provider::findOrFail($appointment->provider_id);

            // Check if the provider is available at the requested time
            $isAvailable = $provider->isAvailable(
                $request->date,
                $request->time_slot,
                $appointment->id // Exclude current appointment from availability check
            );

            if (!$isAvailable) {
                \Log::warning('Provider ' . $provider->id . ' is not available at the requested time: ' .
                    $request->date . ' ' . json_encode($request->time_slot));

                return response()->json([
                    'message' => 'Provider is not available at the requested time'
                ], 400);
            }

            // Update appointment date and time
            $appointment->date = $request->date;
            $appointment->time_slot = $request->time_slot;

            // If appointment was cancelled, set it back to scheduled
            if ($appointment->status === 'cancelled') {
                $appointment->status = 'scheduled';
            }
        }

        // Update status if provided
        if ($request->has('status')) {
            $appointment->status = $request->status;
        }

        // Update notes if provided
        if ($request->has('notes')) {
            $appointment->notes = $request->notes;
        }

        $appointment->save();

        // Send notifications based on the changes
        try {
            $patient = Patient::findOrFail($appointment->patient_id);
            $provider = Provider::findOrFail($appointment->provider_id);

            // If appointment was rescheduled
            if (($request->has('date') || $request->has('time_slot')) &&
                ($oldDate != $appointment->date || $oldTimeSlot != $appointment->time_slot)) {

                // Send notification to patient
                $patient->user->notify(new AppointmentRescheduledNotification(
                    $appointment,
                    $oldDate,
                    $oldTimeSlot,
                    false
                ));

                // Send notification to provider
                $provider->user->notify(new AppointmentRescheduledNotification(
                    $appointment,
                    $oldDate,
                    $oldTimeSlot,
                    true
                ));

                Log::info('Appointment rescheduled notifications sent', [
                    'appointment_id' => $appointment->id,
                    'old_date' => $oldDate,
                    'new_date' => $appointment->date
                ]);
            }
            // If appointment was cancelled
            else if ($request->has('status') && $request->status === 'cancelled' && $oldStatus !== 'cancelled') {
                // Get cancellation reason from notes if provided
                $reason = $request->notes ?? null;

                // Send notification to patient
                $patient->user->notify(new AppointmentCancelledNotification(
                    $appointment,
                    $reason,
                    false
                ));

                // Send notification to provider
                $provider->user->notify(new AppointmentCancelledNotification(
                    $appointment,
                    $reason,
                    true
                ));

                Log::info('Appointment cancelled notifications sent', [
                    'appointment_id' => $appointment->id,
                    'reason' => $reason
                ]);
            }
        } catch (\Exception $e) {
            Log::error('Failed to send appointment update notification', [
                'appointment_id' => $appointment->id,
                'error' => $e->getMessage()
            ]);
        }

        return response()->json([
            'message' => 'Appointment updated successfully',
            'appointment' => $appointment
        ]);
    }

    /**
     * Delete an appointment
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\Response
     */
    /**
     * Get a specific appointment by ID
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\Response
     */
    public function show(Request $request, $id)
    {
        $user = $request->user();
        $appointment = Appointment::with(['provider.user', 'patient.user', 'service'])->findOrFail($id);

        // Verify the user is authorized to view this appointment
        if ($user->role === 'patient') {
            $patient = Patient::where('user_id', $user->id)->first();

            if (!$patient || $appointment->patient_id !== $patient->id) {
                return response()->json([
                    'message' => 'Unauthorized'
                ], 403);
            }
        } elseif ($user->role === 'provider') {
            $provider = Provider::where('user_id', $user->id)->first();

            if (!$provider || $appointment->provider_id !== $provider->id) {
                return response()->json([
                    'message' => 'Unauthorized'
                ], 403);
            }
        } else {
            return response()->json([
                'message' => 'Unauthorized'
            ], 403);
        }

        return response()->json([
            'appointment' => $appointment
        ]);
    }

    public function destroy(Request $request, $id)
    {
        $user = $request->user();
        $appointment = Appointment::findOrFail($id);

        // Verify the user is authorized to delete this appointment
        if ($user->role === 'patient') {
            $patient = Patient::where('user_id', $user->id)->first();

            if ($appointment->patient_id !== $patient->id) {
                return response()->json([
                    'message' => 'Unauthorized'
                ], 403);
            }
        } elseif ($user->role === 'provider') {
            $provider = Provider::where('user_id', $user->id)->first();

            if ($appointment->provider_id !== $provider->id) {
                return response()->json([
                    'message' => 'Unauthorized'
                ], 403);
            }
        } else {
            return response()->json([
                'message' => 'Unauthorized'
            ], 403);
        }

        // Delete the appointment
        $appointment->delete();

        return response()->json([
            'message' => 'Appointment deleted successfully'
        ]);
    }

    /**
     * Permanently delete an appointment (for completed/cancelled appointments)
     */
    public function permanentDelete(Request $request, $id)
    {
        $user = $request->user();
        $appointment = Appointment::findOrFail($id);

        // Verify the user is authorized to delete this appointment
        if ($user->role === 'patient') {
            $patient = Patient::where('user_id', $user->id)->first();

            if ($appointment->patient_id !== $patient->id) {
                return response()->json([
                    'message' => 'Unauthorized'
                ], 403);
            }
        } elseif ($user->role === 'provider') {
            $provider = Provider::where('user_id', $user->id)->first();

            if ($appointment->provider_id !== $provider->id) {
                return response()->json([
                    'message' => 'Unauthorized'
                ], 403);
            }
        } elseif (!in_array($user->role, ['admin', 'super_admin'])) {
            return response()->json([
                'message' => 'Unauthorized'
            ], 403);
        }

        // Only allow deletion of completed or cancelled appointments
        if (!in_array($appointment->status, ['completed', 'cancelled'])) {
            return response()->json([
                'message' => 'Only completed or cancelled appointments can be permanently deleted'
            ], 400);
        }

        // Permanently delete the appointment
        $appointment->forceDelete();

        return response()->json([
            'message' => 'Appointment permanently deleted successfully'
        ]);
    }

    public function getAvailableSlots(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'provider_id' => 'nullable|exists:providers,id',
            'start_date' => 'required|date|after_or_equal:today',
            'end_date' => 'required|date|after_or_equal:start_date',
            'service_id' => 'nullable|exists:services,id',
        ]);

        // Log the request for debugging
        \Log::info('Available slots request: ' . json_encode($request->all()));

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        // Get the current user's appointment preferences if they are a patient
        $user = $request->user();
        $patientPreferences = null;

        if ($user && $user->role === 'patient') {
            $patient = Patient::where('user_id', $user->id)->first();
            if ($patient && !empty($patient->appointment_preferences)) {
                $patientPreferences = $patient->appointment_preferences;
            }
        }

        // If no specific provider is requested, find providers based on preferences
        if (!$request->has('provider_id')) {
            $providersQuery = Provider::query();

            // Filter by service if specified
            if ($request->has('service_id')) {
                $service = Service::findOrFail($request->service_id);
                $providersQuery->where('specialization', $service->specialization);
            }

            // Apply patient preferences to filter providers if available
            if ($patientPreferences) {
                // Filter by preferred gender if specified
                if (!empty($patientPreferences['preferred_gender']) && $patientPreferences['preferred_gender'] !== 'any') {
                    $providersQuery->where('gender', $patientPreferences['preferred_gender']);
                }

                // Filter by preferred language if specified
                if (!empty($patientPreferences['preferred_language'])) {
                    $providersQuery->where(function($query) use ($patientPreferences) {
                        $query->whereJsonContains('languages', $patientPreferences['preferred_language'])
                              ->orWhereNull('languages'); // Include providers with no language specified
                    });
                }

                // Filter by location coordinates if available
                if (!empty($patientPreferences['latitude']) && !empty($patientPreferences['longitude'])) {
                    $userLat = (float) $patientPreferences['latitude'];
                    $userLng = (float) $patientPreferences['longitude'];
                    $searchRadius = (int) ($patientPreferences['search_radius'] ?? 25);

                    // Get all providers and filter by distance
                    // Note: In a production environment with many providers, this should be optimized
                    // with a proper geospatial query using MongoDB's $geoNear or similar
                    $providers = $providersQuery->get();
                    $nearbyProviders = $providers->filter(function($provider) use ($userLat, $userLng, $searchRadius) {
                        // Find the closest practice location
                        $minDistance = PHP_FLOAT_MAX;
                        foreach ($provider->practice_locations as $location) {
                            if (isset($location['coordinates']) && count($location['coordinates']) == 2) {
                                $providerLat = (float) $location['coordinates'][0];
                                $providerLng = (float) $location['coordinates'][1];

                                // Calculate distance using Haversine formula (more accurate for geographic distances)
                                $earthRadius = 6371; // in kilometers
                                $latDelta = deg2rad($providerLat - $userLat);
                                $lngDelta = deg2rad($providerLng - $userLng);

                                $a = sin($latDelta/2) * sin($latDelta/2) +
                                     cos(deg2rad($userLat)) * cos(deg2rad($providerLat)) *
                                     sin($lngDelta/2) * sin($lngDelta/2);

                                $c = 2 * atan2(sqrt($a), sqrt(1-$a));
                                $distance = $earthRadius * $c;

                                if ($distance < $minDistance) {
                                    $minDistance = $distance;
                                    // Store the distance for later use
                                    $provider->distance = $distance;
                                }
                            }
                        }

                        // Return true if within search radius (this keeps the provider in the filtered collection)
                        return $minDistance <= $searchRadius;
                    });

                    // If no providers within radius, we'll still return all providers but mark them as distant
                    if ($nearbyProviders->isEmpty()) {
                        // Continue with all providers
                    } else {
                        // Replace the query with only nearby providers
                        $providerIds = $nearbyProviders->pluck('id')->toArray();
                        $providersQuery = Provider::whereIn('id', $providerIds);
                    }
                }
                // Also check for text-based location if coordinates aren't available
                else if (!empty($patientPreferences['preferred_location'])) {
                    $providersQuery->where(function($query) use ($patientPreferences) {
                        $query->whereJsonContains('practice_locations->address', $patientPreferences['preferred_location'])
                              ->orWhereJsonContains('practice_locations->city', $patientPreferences['preferred_location']);
                    });
                }
            }

            // Get the first available provider or return an error if none found
            $provider = $providersQuery->first();

            if (!$provider) {
                return response()->json([
                    'message' => 'No providers found matching your criteria',
                    'preferences' => $patientPreferences
                ], 404);
            }
        } else {
            $provider = Provider::findOrFail($request->provider_id);
            // Refresh the provider model to ensure we have the latest data
            $provider->refresh();
        }

        // Ensure start_date is not in the past
        $today = date('Y-m-d');
        $startDate = $request->start_date;
        if ($startDate < $today) {
            $startDate = $today;
            \Log::info("Adjusted start_date to today: {$today}");
        }

        $endDate = $request->end_date;
        if ($endDate < $today) {
            $endDate = $today;
            \Log::info("Adjusted end_date to today: {$today}");
        }

        // Calculate the number of days in the range
        $startDateObj = new \DateTime($startDate);
        $endDateObj = new \DateTime($endDate);
        $interval = $startDateObj->diff($endDateObj);
        $numDays = $interval->days + 1; // Include both start and end dates

        // Limit to a maximum of 14 days to prevent excessive processing
        $numDays = min($numDays, 14);

        \Log::info("Date range: {$startDate} to {$endDate}, {$numDays} days");

        $availableSlots = [];

        // Log provider's absences to debug
        \Log::info('Provider absences: ' . json_encode($provider->getAbsencesData()));

        // Get available slots for each day in the range
        for ($i = 0; $i < $numDays; $i++) {
            $date = date('Y-m-d', strtotime("$startDate +$i days"));

            // Check if provider is absent on this date and log it
            $dateObj = \Carbon\Carbon::parse($date);
            $isAbsent = $provider->isAbsent($dateObj);
            \Log::info("Checking date {$date}: Provider is absent? " . ($isAbsent ? 'Yes' : 'No'));

            // Check provider's weekly availability for this day
            $dayOfWeek = $dateObj->format('l');
            $weeklyAvailability = $provider->getAvailabilityData();
            $dayAvailability = collect($weeklyAvailability)->firstWhere('day', $dayOfWeek);
            \Log::info("Weekly availability for {$dayOfWeek}: " . json_encode($dayAvailability));

            // Get available slots with service ID if provided
            // This will check for conflicts with ALL existing appointments
            $serviceId = $request->has('service_id') ? $request->service_id : null;
            $daySlots = $provider->getAvailableTimeSlots($date, $serviceId);
            \Log::info("Available slots for {$date} with service ID {$serviceId}: " . json_encode($daySlots));

            // Log the number of available slots
            \Log::info("Found " . count($daySlots) . " available slots for {$date} with service ID {$serviceId}");

            if (!empty($daySlots)) {
                $availableSlots[$date] = array_values($daySlots);
            }
        }

        // Get current server time for time zone reference
        $now = \Carbon\Carbon::now();

        $responseData = [
            'provider_id' => $provider->id,
            'provider_name' => $provider->user->name,
            'start_date' => $startDate,
            'end_date' => $endDate,
            'server_time' => [
                'date' => $now->format('Y-m-d'),
                'time' => $now->format('H:i:s'),
                'timezone' => $now->getTimezone()->getName(),
            ],
            'available_slots' => $availableSlots
        ];

        // Add distance information if available
        if (isset($provider->distance)) {
            $responseData['distance'] = round($provider->distance, 1);
            $responseData['distance_unit'] = 'km';

            // Check if provider is outside the preferred search radius
            if (!empty($patientPreferences['search_radius'])) {
                $searchRadius = (int) $patientPreferences['search_radius'];
                $responseData['outside_preferred_radius'] = $provider->distance > $searchRadius;
            }
        }

        return response()->json($responseData);
    }

    /**
     * Get available slots for a specific provider (used by anonymous chat)
     *
     * @param int $providerId
     * @param string $startDate
     * @param string $endDate
     * @return array
     */
    public function getProviderAvailableSlots($providerId, $startDate, $endDate)
    {
        try {
            $provider = Provider::findOrFail($providerId);
            
            // Calculate the number of days in the range
            $startDateObj = new \DateTime($startDate);
            $endDateObj = new \DateTime($endDate);
            $interval = $startDateObj->diff($endDateObj);
            $numDays = $interval->days + 1; // Include both start and end dates
            
            // Limit to a maximum of 14 days to prevent excessive processing
            $numDays = min($numDays, 14);
            
            $availableSlots = [];
            
            // Get available slots for each day in the range
            for ($i = 0; $i < $numDays; $i++) {
                $date = date('Y-m-d', strtotime("$startDate +$i days"));
                
                // Get available slots for this date
                $daySlots = $provider->getAvailableTimeSlots($date);
                
                if (!empty($daySlots)) {
                    $availableSlots[] = [
                        'provider' => [
                            'id' => $provider->id,
                            'name' => $provider->user ? $provider->user->name : 'Provider ' . $provider->id,
                            'specialization' => $provider->specialization,
                        ],
                        'date' => $date,
                        'day_of_week' => date('l', strtotime($date)),
                        'slots' => array_values($daySlots),
                    ];
                }
            }
            
            return $availableSlots;
            
        } catch (\Exception $e) {
            \Log::error('Error getting provider available slots: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * Display a listing of the appointments for management.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        // Check if user has permission to view appointments
        if (!$request->user()->can('view appointments')) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $query = Appointment::with(['patient.user', 'provider.user', 'service']);

        // Filter by status if provided
        if ($request->has('status') && $request->status) {
            $query->where('status', $request->status);
        }

        // Filter by date range if provided
        if ($request->has('start_date') && $request->start_date) {
            $query->where('scheduled_at', '>=', Carbon::parse($request->start_date)->startOfDay());
        }

        if ($request->has('end_date') && $request->end_date) {
            $query->where('scheduled_at', '<=', Carbon::parse($request->end_date)->endOfDay());
        }

        // Filter by provider if provided
        if ($request->has('provider_id') && $request->provider_id) {
            $query->where('provider_id', $request->provider_id);
        }

        // Filter by patient if provided
        if ($request->has('patient_id') && $request->patient_id) {
            $query->where('patient_id', $request->patient_id);
        }

        // Filter by search term if provided
        if ($request->has('search') && $request->search) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->whereHas('patient.user', function($q) use ($search) {
                      $q->where('name', 'like', "%{$search}%");
                  })
                  ->orWhereHas('provider.user', function($q) use ($search) {
                      $q->where('name', 'like', "%{$search}%");
                  });
            });
        }

        // Sort by column
        $sortBy = $request->input('sort_by', 'scheduled_at');
        $sortDir = $request->input('sort_dir', 'desc');
        $query->orderBy($sortBy, $sortDir);

        // Paginate results
        $perPage = $request->input('per_page', 15);
        $appointments = $query->paginate($perPage);

        return response()->json($appointments);
    }

    /**
     * Get appointment statistics and analytics.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getStatistics(Request $request)
    {
        // Check if user has permission to view appointments
        if (!$request->user()->can('view appointments')) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        // Get date range from request or default to last 30 days
        $startDate = $request->input('start_date')
            ? Carbon::parse($request->input('start_date'))
            : Carbon::now()->subDays(30);

        $endDate = $request->input('end_date')
            ? Carbon::parse($request->input('end_date'))
            : Carbon::now();

        // Get total appointments
        $totalAppointments = Appointment::whereBetween('scheduled_at', [$startDate, $endDate])->count();

        // Get appointments by status
        $appointmentsByStatus = Appointment::whereBetween('scheduled_at', [$startDate, $endDate])
            ->selectRaw('status, count(*) as count')
            ->groupBy('status')
            ->get();

        // Get appointments by day
        $appointmentsByDay = Appointment::whereBetween('scheduled_at', [$startDate, $endDate])
            ->selectRaw('DATE(scheduled_at) as date, count(*) as count')
            ->groupBy(DB::raw('DATE(scheduled_at)'))
            ->get()
            ->map(function ($item) {
                return [
                    'date' => $item->date,
                    'count' => $item->count,
                ];
            });

        // Get top providers by appointment count
        $topProviders = Appointment::whereBetween('scheduled_at', [$startDate, $endDate])
            ->with('provider.user')
            ->selectRaw('provider_id, count(*) as count')
            ->groupBy('provider_id')
            ->orderBy('count', 'desc')
            ->limit(5)
            ->get()
            ->map(function ($item) {
                return [
                    'provider_id' => $item->provider_id,
                    'provider_name' => $item->provider->user->name,
                    'count' => $item->count,
                ];
            });

        return response()->json([
            'total_appointments' => $totalAppointments,
            'appointments_by_status' => $appointmentsByStatus,
            'appointments_by_day' => $appointmentsByDay,
            'top_providers' => $topProviders,
            'date_range' => [
                'start_date' => $startDate->toDateString(),
                'end_date' => $endDate->toDateString(),
            ],
        ]);
    }

    /**
     * Display the appointments page (web route)
     */
    public function webIndex(Request $request): Response
    {
        return Inertia::render('Appointments', [
            'auth' => [
                'user' => $request->user()
            ]
        ]);
    }



    /**
     * Display the create appointment page (web route)
     */
    public function webCreate(Request $request): Response
    {
        return Inertia::render('AppointmentCreate');
    }

    /**
     * Display a specific appointment (web route)
     */
    public function webShow(Request $request, Appointment $appointment): Response
    {
        $user = $request->user();
        $appointmentData = $this->appointmentService->getAppointment($appointment->id, $user);

        if (!$appointmentData) {
            abort(404);
        }

        return Inertia::render('AppointmentDetail', [
            'appointment' => $appointmentData
        ]);
    }

    /**
     * Display the edit appointment page (web route)
     */
    public function webEdit(Request $request, Appointment $appointment): Response
    {
        $user = $request->user();
        $appointmentData = $this->appointmentService->getAppointment($appointment->id, $user);

        if (!$appointmentData) {
            abort(404);
        }

        // Check if user can edit this appointment
        if ($appointmentData['status'] !== 'scheduled') {
            abort(403, 'Cannot edit appointment with status: ' . $appointmentData['status']);
        }

        return Inertia::render('AppointmentEdit', [
            'appointment' => $appointmentData
        ]);
    }

    /**
     * Get user appointments (API route using service)
     */
    public function getUserAppointmentsApi(Request $request)
    {
        $user = $request->user();
        $filters = [
            'status' => $request->input('status'),
            'date_from' => $request->input('date_from'),
            'date_to' => $request->input('date_to')
        ];

        $perPage = $request->input('per_page', 15);
        $appointments = $this->appointmentService->getUserAppointments($user, $filters, $perPage);

        return response()->json([
            'appointments' => $appointments
        ]);
    }

    /**
     * Create appointment (API route using service)
     */
    public function createAppointmentApi(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'provider_id' => 'required|exists:providers,id',
            'scheduled_at' => 'required|date|after:now',
            'duration' => 'nullable|integer|min:15|max:180',
            'is_telemedicine' => 'boolean',
            'reason' => 'nullable|string|max:500',
            'notes' => 'nullable|string|max:1000',
            'amount' => 'nullable|numeric|min:0'
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $user = $request->user();
        $appointment = $this->appointmentService->createAppointment($request->all(), $user);

        if (!$appointment) {
            return response()->json(['error' => 'Failed to create appointment'], 500);
        }

        return response()->json([
            'appointment' => $appointment,
            'message' => 'Appointment created successfully'
        ], 201);
    }

    /**
     * Cancel appointment (API route using service)
     */
    public function cancelAppointmentApi(Request $request, $appointmentId)
    {
        $user = $request->user();
        $reason = $request->input('reason');

        $success = $this->appointmentService->cancelAppointment($appointmentId, $user, $reason);

        if (!$success) {
            return response()->json(['error' => 'Failed to cancel appointment'], 400);
        }

        return response()->json(['message' => 'Appointment cancelled successfully']);
    }
}