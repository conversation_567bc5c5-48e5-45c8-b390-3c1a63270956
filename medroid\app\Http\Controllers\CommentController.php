<?php

namespace App\Http\Controllers;

use App\Models\SocialContent;
use App\Models\SocialContentComment;
use App\Models\CommentReaction;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class CommentController extends Controller
{
    /**
     * Get comments for a specific social content post.
     */
    public function index(Request $request, $socialContentId)
    {
        try {
            $socialContent = SocialContent::findOrFail($socialContentId);

            $comments = SocialContentComment::with(['user', 'replies.user'])
                ->where('social_content_id', $socialContentId)
                ->whereNull('parent_id') // Only get top-level comments
                ->orderBy('created_at', 'desc')
                ->paginate($request->get('per_page', 20));

            return response()->json([
                'success' => true,
                'comments' => $comments->items(),
                'pagination' => [
                    'current_page' => $comments->currentPage(),
                    'last_page' => $comments->lastPage(),
                    'per_page' => $comments->perPage(),
                    'total' => $comments->total(),
                    'has_more' => $comments->hasMorePages(),
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to load comments',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Create a new comment.
     */
    public function store(Request $request, $socialContentId)
    {
        try {
            $validator = Validator::make($request->all(), [
                'content' => 'required|string|max:1000',
                'parent_id' => 'nullable|exists:social_content_comments,id'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $socialContent = SocialContent::findOrFail($socialContentId);
            $user = $request->user();

            $comment = SocialContentComment::create([
                'social_content_id' => $socialContentId,
                'user_id' => $user->id,
                'content' => $request->content,
                'parent_id' => $request->parent_id,
            ]);

            // Load the comment with user relationship
            $comment->load('user', 'replies.user');

            // Update engagement metrics
            $engagementMetrics = $socialContent->engagement_metrics ?? [];
            $engagementMetrics['comments'] = ($engagementMetrics['comments'] ?? 0) + 1;
            $socialContent->update(['engagement_metrics' => $engagementMetrics]);

            return response()->json([
                'success' => true,
                'message' => 'Comment posted successfully',
                'comment' => $comment
            ], 201);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to post comment',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update a comment.
     */
    public function update(Request $request, $socialContentId, $commentId)
    {
        try {
            $validator = Validator::make($request->all(), [
                'content' => 'required|string|max:1000'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $comment = SocialContentComment::where('id', $commentId)
                ->where('social_content_id', $socialContentId)
                ->where('user_id', $request->user()->id)
                ->firstOrFail();

            $comment->update([
                'content' => $request->content
            ]);

            $comment->load('user', 'replies.user');

            return response()->json([
                'success' => true,
                'message' => 'Comment updated successfully',
                'comment' => $comment
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update comment',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Delete a comment.
     */
    public function destroy(Request $request, $socialContentId, $commentId)
    {
        try {
            $comment = SocialContentComment::where('id', $commentId)
                ->where('social_content_id', $socialContentId)
                ->where('user_id', $request->user()->id)
                ->firstOrFail();

            $socialContent = SocialContent::findOrFail($socialContentId);

            // Count total comments being deleted (including replies)
            $totalCommentsToDelete = 1 + $comment->replies()->count();

            $comment->delete(); // This will cascade delete replies

            // Update engagement metrics
            $engagementMetrics = $socialContent->engagement_metrics ?? [];
            $engagementMetrics['comments'] = max(0, ($engagementMetrics['comments'] ?? 0) - $totalCommentsToDelete);
            $socialContent->update(['engagement_metrics' => $engagementMetrics]);

            return response()->json([
                'success' => true,
                'message' => 'Comment deleted successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to delete comment',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get replies for a specific comment.
     */
    public function getReplies(Request $request, $socialContentId, $commentId)
    {
        try {
            $replies = SocialContentComment::with('user')
                ->where('parent_id', $commentId)
                ->where('social_content_id', $socialContentId)
                ->orderBy('created_at', 'asc')
                ->get();

            return response()->json([
                'success' => true,
                'replies' => $replies
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to load replies',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * React to a comment.
     */
    public function react(Request $request, $socialContentId, $commentId)
    {
        try {
            $validator = Validator::make($request->all(), [
                'reaction_type' => 'required|in:like,love,laugh,angry,sad,wow'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $comment = SocialContentComment::where('id', $commentId)
                ->where('social_content_id', $socialContentId)
                ->firstOrFail();

            $user = $request->user();
            $reactionType = $request->reaction_type;

            // Check if user already reacted to this comment
            $existingReaction = CommentReaction::where('comment_id', $commentId)
                ->where('user_id', $user->id)
                ->first();

            if ($existingReaction) {
                if ($existingReaction->reaction_type === $reactionType) {
                    // Same reaction - remove it (toggle off)
                    $existingReaction->delete();
                    $reacted = false;
                } else {
                    // Different reaction - update it
                    $existingReaction->update(['reaction_type' => $reactionType]);
                    $reacted = true;
                }
            } else {
                // New reaction
                CommentReaction::create([
                    'comment_id' => $commentId,
                    'user_id' => $user->id,
                    'reaction_type' => $reactionType,
                ]);
                $reacted = true;
            }

            // Get updated reaction counts
            $reactionCounts = CommentReaction::where('comment_id', $commentId)
                ->selectRaw('reaction_type, COUNT(*) as count')
                ->groupBy('reaction_type')
                ->pluck('count', 'reaction_type')
                ->toArray();

            // Get user's current reaction
            $userReaction = CommentReaction::where('comment_id', $commentId)
                ->where('user_id', $user->id)
                ->first();

            return response()->json([
                'success' => true,
                'message' => $reacted ? 'Reaction added' : 'Reaction removed',
                'reacted' => $reacted,
                'reaction_type' => $reactionType,
                'reaction_counts' => $reactionCounts,
                'user_reaction' => $userReaction ? $userReaction->reaction_type : null
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to react to comment',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Create a reply to a comment.
     */
    public function storeReply(Request $request, $socialContentId, $commentId)
    {
        try {
            $validator = Validator::make($request->all(), [
                'content' => 'required|string|max:1000'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $socialContent = SocialContent::findOrFail($socialContentId);
            $parentComment = SocialContentComment::where('id', $commentId)
                ->where('social_content_id', $socialContentId)
                ->firstOrFail();

            $user = $request->user();

            $reply = SocialContentComment::create([
                'social_content_id' => $socialContentId,
                'user_id' => $user->id,
                'content' => $request->content,
                'parent_id' => $commentId,
            ]);

            // Load the reply with user relationship
            $reply->load('user');

            // Update engagement metrics
            $engagementMetrics = $socialContent->engagement_metrics ?? [];
            $engagementMetrics['comments'] = ($engagementMetrics['comments'] ?? 0) + 1;
            $socialContent->update(['engagement_metrics' => $engagementMetrics]);

            return response()->json([
                'success' => true,
                'message' => 'Reply posted successfully',
                'comment' => $reply
            ], 201);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to post reply',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get real-time comment count for a post.
     */
    public function getCommentCount(Request $request, $socialContentId)
    {
        try {
            $socialContent = SocialContent::findOrFail($socialContentId);

            $commentCount = SocialContentComment::where('social_content_id', $socialContentId)->count();

            return response()->json([
                'success' => true,
                'comment_count' => $commentCount
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to get comment count',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
