<?php

namespace App\Http\Controllers;

use Inertia\Inertia;
use Inertia\Response;

class WebController extends Controller
{
    /**
     * Display the home page.
     */
    public function index(): Response
    {
        return Inertia::render('Welcome');
    }

    /**
     * Display the terms and conditions page.
     */
    public function termsAndConditions()
    {
        return view('legal.terms-and-conditions');
    }

    /**
     * Display the privacy policy page.
     */
    public function privacyPolicy()
    {
        return view('legal.privacy-policy');
    }

    /**
     * Display the discover page.
     */
    public function discover(): Response
    {
        // Get initial feed data for the page
        $feedController = new \App\Http\Controllers\SocialFeedController();
        $storyController = new \App\Http\Controllers\StoryController();
        $request = request();
        $request->merge(['sort_by' => 'relevance', 'per_page' => 10]);

        try {
            $feedResponse = $feedController->index($request);
            $feedData = json_decode($feedResponse->getContent(), true);

            // Get available topics
            $topicsResponse = $feedController->topics();
            $topicsData = json_decode($topicsResponse->getContent(), true);

            // Get stories data
            $storiesResponse = $storyController->index($request);
            $storiesData = json_decode($storiesResponse->getContent(), true);

            return Inertia::render('Discover', [
                'initialFeed' => $feedData,
                'availableTopics' => $topicsData['topics'] ?? [],
                'initialStories' => $storiesData['stories'] ?? [],
            ]);
        } catch (\Exception $e) {
            // Fallback to empty data if there's an error
            return Inertia::render('Discover', [
                'initialFeed' => [
                    'data' => [],
                    'current_page' => 1,
                    'last_page' => 1,
                    'total' => 0
                ],
                'availableTopics' => [],
                'initialStories' => [],
            ]);
        }
    }

    /**
     * Display the shop page.
     */
    public function shop(): Response
    {
        return Inertia::render('Shop');
    }

    /**
     * Display the chat history page.
     */
    public function chatHistory(): Response
    {
        return Inertia::render('ChatHistory');
    }

    /**
     * Display the credit history page.
     */
    public function creditHistory(): Response
    {
        return Inertia::render('CreditHistory');
    }
}
