<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('payment_history', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->foreignId('appointment_id')->nullable()->constrained()->onDelete('set null');
            $table->string('payment_id')->unique(); // Stripe payment intent ID
            $table->decimal('amount', 10, 2);
            $table->string('currency', 3)->default('gbp');
            $table->string('payment_method_type')->nullable(); // card, bank_transfer, etc.
            $table->string('payment_method_details')->nullable(); // last4, brand, etc.
            $table->string('status'); // succeeded, pending, failed, refunded
            $table->string('description')->nullable();
            $table->json('metadata')->nullable();
            $table->timestamp('paid_at')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('payment_history');
    }
};
