<script setup>
import AppLayout from '@/layouts/AppLayout.vue';
import { Head } from '@inertiajs/vue3';
import { ref, onMounted, computed } from 'vue';

const breadcrumbs = [
    { title: 'Dashboard', href: '/dashboard' },
    { title: 'Credits', href: '/credits' },
];

const loading = ref(false);
const credits = ref([]);
const transactions = ref([]);
const totalCredits = ref(0);
const stats = ref({
    total_earned: 0,
    total_used: 0,
    current_balance: 0,
    total_transactions: 0,
    users_with_credits: 0
});
const showAddCreditModal = ref(false);
const searchLoading = ref(false);
const users = ref([]);
const searchQuery = ref('');

// Add Credit Form
const addCreditForm = ref({
    user_id: '',
    amount: '',
    source: 'admin',
    description: ''
});

// Credit Types/Sources
const creditTypes = [
    { value: 'admin', label: 'Admin Credit' },
    { value: 'referral', label: 'Referral Bonus' },
    { value: 'promotion', label: 'Promotional Credit' },
    { value: 'bonus', label: 'Bonus Credit' },
    { value: 'compensation', label: 'Compensation' },
    { value: 'welcome', label: 'Welcome Bonus' }
];

// Filters
const filters = ref({
    user_id: '',
    type: '',
    source: '',
    search: ''
});

const selectedUser = ref(null);

const fetchCredits = async () => {
    loading.value = true;
    try {
        const response = await window.axios.get('/credits-list');
        credits.value = response.data.data || [];
    } catch (error) {
        console.error('Error fetching credits:', error);
        credits.value = [];
    } finally {
        loading.value = false;
    }
};

const fetchTransactions = async () => {
    try {
        const params = new URLSearchParams();
        if (filters.value.user_id) params.append('user_id', filters.value.user_id);
        if (filters.value.type) params.append('type', filters.value.type);
        if (filters.value.source) params.append('source', filters.value.source);

        const response = await window.axios.get(`/credits-all-transactions?${params}`);
        transactions.value = response.data.data || [];
    } catch (error) {
        console.error('Error fetching transactions:', error);
        transactions.value = [];
    }
};

const fetchStats = async () => {
    try {
        const response = await window.axios.get('/credits-stats');
        stats.value = response.data;
    } catch (error) {
        console.error('Error fetching stats:', error);
        stats.value = {
            total_earned: 0,
            total_used: 0,
            current_balance: 0,
            total_transactions: 0,
            users_with_credits: 0
        };
    }
};

const searchUsers = async (query) => {
    if (!query || query.length < 2) {
        users.value = [];
        return;
    }

    searchLoading.value = true;
    try {
        const response = await window.axios.get('/users-search', {
            params: { search: query, limit: 10 }
        });
        users.value = response.data.data || [];
    } catch (error) {
        console.error('Error searching users:', error);
        users.value = [];
    } finally {
        searchLoading.value = false;
    }
};

const addCredit = async () => {
    if (!addCreditForm.value.user_id || !addCreditForm.value.amount) {
        alert('Please fill in all required fields');
        return;
    }

    try {
        const response = await window.axios.post('/credits-add', addCreditForm.value);

        if (response.data.success) {
            alert('Credit added successfully!');
            showAddCreditModal.value = false;
            resetAddCreditForm();
            await fetchCredits();
            await fetchTransactions();
            await fetchStats();
        } else {
            alert(response.data.message || 'Failed to add credit');
        }
    } catch (error) {
        console.error('Error adding credit:', error);
        alert('Failed to add credit. Please try again.');
    }
};

const resetAddCreditForm = () => {
    addCreditForm.value = {
        user_id: '',
        amount: '',
        source: 'admin',
        description: ''
    };
    selectedUser.value = null;
    searchQuery.value = '';
    users.value = [];
};

const selectUser = (user) => {
    selectedUser.value = user;
    addCreditForm.value.user_id = user.id;
    searchQuery.value = user.name;
    users.value = [];
};

const applyFilters = () => {
    fetchTransactions();
};

const clearFilters = () => {
    filters.value = {
        user_id: '',
        type: '',
        source: '',
        search: ''
    };
    fetchTransactions();
};

onMounted(() => {
    fetchCredits();
    fetchTransactions();
    fetchStats();
});

// Computed properties - use stats from backend instead of calculating from frontend data
const totalCreditsAmount = computed(() => {
    return stats.value.total_earned || 0;
});

const totalUsedAmount = computed(() => {
    return stats.value.total_used || 0;
});

const currentBalance = computed(() => {
    return stats.value.current_balance || 0;
});

const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-GB', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    });
};

const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-GB', {
        style: 'currency',
        currency: 'GBP'
    }).format(amount);
};

const getTypeClass = (type) => {
    const classes = {
        earned: 'bg-green-100 text-green-800',
        used: 'bg-red-100 text-red-800',
        expired: 'bg-gray-100 text-gray-800',
        refunded: 'bg-blue-100 text-blue-800'
    };
    return classes[type] || 'bg-gray-100 text-gray-800';
};

const getSourceClass = (source) => {
    const classes = {
        admin: 'bg-purple-100 text-purple-800',
        referral: 'bg-pink-100 text-pink-800',
        promotion: 'bg-yellow-100 text-yellow-800',
        bonus: 'bg-indigo-100 text-indigo-800',
        compensation: 'bg-orange-100 text-orange-800',
        welcome: 'bg-teal-100 text-teal-800'
    };
    return classes[source] || 'bg-gray-100 text-gray-800';
};
</script>

<template>
    <Head title="Credits Management" />

    <AppLayout :breadcrumbs="breadcrumbs">
        <div class="p-6">
            <div class="mb-6 flex justify-between items-center">
                <div>
                    <h1 class="text-2xl font-bold text-gray-900">Credits Management</h1>
                    <p class="text-gray-600">Manage user credits and transaction history</p>
                </div>
                <button
                    @click="showAddCreditModal = true"
                    class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center"
                >
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
                    </svg>
                    Add Credit
                </button>
            </div>

            <!-- Stats Cards -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center">
                        <div class="p-2 bg-green-100 rounded-lg">
                            <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">Total Earned</p>
                            <p class="text-2xl font-bold text-gray-900">{{ formatCurrency(totalCreditsAmount) }}</p>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center">
                        <div class="p-2 bg-red-100 rounded-lg">
                            <svg class="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 14l-7 7m0 0l-7-7m7 7V3" />
                            </svg>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">Total Used</p>
                            <p class="text-2xl font-bold text-gray-900">{{ formatCurrency(totalUsedAmount) }}</p>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center">
                        <div class="p-2 bg-blue-100 rounded-lg">
                            <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                            </svg>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">Current Balance</p>
                            <p class="text-2xl font-bold text-gray-900">{{ formatCurrency(currentBalance) }}</p>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center">
                        <div class="p-2 bg-purple-100 rounded-lg">
                            <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                            </svg>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">Transactions</p>
                            <p class="text-2xl font-bold text-gray-900">{{ stats.total_transactions }}</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Filters -->
            <div class="bg-white rounded-lg shadow p-6 mb-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Filter Transactions</h3>
                <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Transaction Type</label>
                        <select v-model="filters.type" class="w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500">
                            <option value="">All Types</option>
                            <option value="earned">Earned</option>
                            <option value="used">Used</option>
                            <option value="expired">Expired</option>
                            <option value="refunded">Refunded</option>
                        </select>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Source</label>
                        <select v-model="filters.source" class="w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500">
                            <option value="">All Sources</option>
                            <option v-for="type in creditTypes" :key="type.value" :value="type.value">{{ type.label }}</option>
                        </select>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">User ID</label>
                        <input v-model="filters.user_id" type="number" placeholder="Enter user ID" class="w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500">
                    </div>
                    <div class="flex items-end space-x-2">
                        <button @click="applyFilters" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md">
                            Apply Filters
                        </button>
                        <button @click="clearFilters" class="bg-gray-300 hover:bg-gray-400 text-gray-700 px-4 py-2 rounded-md">
                            Clear
                        </button>
                    </div>
                </div>
            </div>

            <!-- User Credits Table -->
            <div class="bg-white rounded-lg shadow mb-6">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h2 class="text-lg font-medium text-gray-900">User Credit Balances</h2>
                </div>
                
                <div v-if="loading" class="p-6">
                    <div class="animate-pulse">
                        <div class="h-4 bg-gray-200 rounded w-3/4 mb-4"></div>
                        <div class="h-4 bg-gray-200 rounded w-1/2 mb-4"></div>
                        <div class="h-4 bg-gray-200 rounded w-5/6"></div>
                    </div>
                </div>

                <div v-else-if="credits.length === 0" class="p-6 text-center text-gray-500">
                    No credit records found.
                </div>

                <div v-else class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">User</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Current Balance</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Total Earned</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Total Spent</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Last Updated</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            <tr v-for="credit in credits" :key="credit.id">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm font-medium text-gray-900">{{ credit.user?.name || 'N/A' }}</div>
                                    <div class="text-sm text-gray-500">{{ credit.user?.email || 'N/A' }}</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                    ${{ parseFloat(credit.balance || 0).toFixed(2) }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-green-600">
                                    ${{ parseFloat(credit.total_earned || 0).toFixed(2) }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-red-600">
                                    ${{ parseFloat(credit.total_spent || 0).toFixed(2) }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    {{ formatDate(credit.updated_at) }}
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Transactions Table -->
            <div class="bg-white rounded-lg shadow">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h2 class="text-lg font-medium text-gray-900">Credit Transactions</h2>
                </div>

                <div v-if="transactions.length === 0" class="p-6 text-center text-gray-500">
                    No transactions found.
                </div>

                <div v-else class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">User</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Source</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            <tr v-for="transaction in transactions" :key="transaction.id">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm font-medium text-gray-900">{{ transaction.user?.name || 'N/A' }}</div>
                                    <div class="text-sm text-gray-500">{{ transaction.user?.email || 'N/A' }}</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span :class="getTypeClass(transaction.type)" class="inline-flex px-2 py-1 text-xs font-semibold rounded-full">
                                        {{ transaction.type }}
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span :class="getSourceClass(transaction.source)" class="inline-flex px-2 py-1 text-xs font-semibold rounded-full">
                                        {{ transaction.source || 'N/A' }}
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                    <span :class="transaction.type === 'earned' ? 'text-green-600' : 'text-red-600'">
                                        {{ transaction.type === 'earned' ? '+' : '-' }}{{ formatCurrency(Math.abs(parseFloat(transaction.amount || 0))) }}
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    {{ transaction.description || 'N/A' }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    {{ formatDate(transaction.created_at) }}
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Add Credit Modal -->
        <div v-if="showAddCreditModal" class="fixed inset-0 bg-white bg-opacity-20 backdrop-blur-sm overflow-y-auto h-full w-full z-50">
            <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-1/2 lg:w-1/3 shadow-lg rounded-md bg-white">
                <div class="mt-3">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Add Credit to User</h3>

                    <form @submit.prevent="addCredit" class="space-y-4">
                        <!-- User Search -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Search User</label>
                            <div class="relative">
                                <input
                                    v-model="searchQuery"
                                    @input="searchUsers(searchQuery)"
                                    type="text"
                                    placeholder="Type user name or email..."
                                    class="w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                                    required
                                >
                                <div v-if="searchLoading" class="absolute right-3 top-3">
                                    <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
                                </div>
                            </div>

                            <!-- User Search Results -->
                            <div v-if="users.length > 0" class="mt-2 max-h-40 overflow-y-auto border border-gray-300 rounded-md bg-white shadow-lg">
                                <div
                                    v-for="user in users"
                                    :key="user.id"
                                    @click="selectUser(user)"
                                    class="p-3 hover:bg-gray-50 cursor-pointer border-b border-gray-100 last:border-b-0"
                                >
                                    <div class="text-sm font-medium text-gray-900">{{ user.name }}</div>
                                    <div class="text-xs text-gray-500">{{ user.email }}</div>
                                </div>
                            </div>

                            <!-- Selected User Display -->
                            <div v-if="selectedUser" class="mt-2 p-3 bg-blue-50 border border-blue-200 rounded-md">
                                <div class="text-sm font-medium text-blue-900">Selected: {{ selectedUser.name }}</div>
                                <div class="text-xs text-blue-700">{{ selectedUser.email }}</div>
                            </div>
                        </div>

                        <!-- Amount -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Amount ($)</label>
                            <input
                                v-model="addCreditForm.amount"
                                type="number"
                                step="0.01"
                                min="0.01"
                                placeholder="0.00"
                                required
                                class="w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                            >
                        </div>

                        <!-- Credit Type/Source -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Credit Type</label>
                            <select
                                v-model="addCreditForm.source"
                                class="w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                            >
                                <option v-for="type in creditTypes" :key="type.value" :value="type.value">
                                    {{ type.label }}
                                </option>
                            </select>
                        </div>

                        <!-- Description -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Description (Optional)</label>
                            <textarea
                                v-model="addCreditForm.description"
                                rows="3"
                                placeholder="Enter description for this credit..."
                                class="w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                            ></textarea>
                        </div>

                        <!-- Buttons -->
                        <div class="flex justify-end space-x-3 pt-4">
                            <button
                                type="button"
                                @click="showAddCreditModal = false; resetAddCreditForm();"
                                class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
                            >
                                Cancel
                            </button>
                            <button
                                type="submit"
                                class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
                            >
                                Add Credit
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </AppLayout>
</template>
