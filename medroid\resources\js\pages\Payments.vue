<script setup>
import AppLayout from '@/layouts/AppLayout.vue';
import { Head, Link } from '@inertiajs/vue3';
import { ref, onMounted, computed } from 'vue';

const breadcrumbs = [
    { title: 'Dashboard', href: '/dashboard' },
    { title: 'Payments', href: '/payments' },
];

const loading = ref(false);
const payments = ref([]);
const showPaymentModal = ref(false);
const selectedPayment = ref(null);
const paymentDetailLoading = ref(false);

const fetchPayments = async () => {
    loading.value = true;
    try {
        const response = await window.axios.get('/payments-list');
        // Handle paginated response - extract the data array
        const paymentsData = response.data.data || response.data || [];

        // Ensure amount is always a number for calculations
        payments.value = paymentsData.map(payment => ({
            ...payment,
            amount: parseFloat(payment.amount) || 0
        }));
    } catch (error) {
        console.error('Error fetching payments:', error);
        payments.value = [];
    } finally {
        loading.value = false;
    }
};

const getStatusBadgeClass = (status) => {
    const classes = {
        paid: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300',
        pending: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300',
        failed: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300',
        refunded: 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300'
    };
    return classes[status] || 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300';
};

// Computed properties for safe calculations
const totalRevenue = computed(() => {
    if (!Array.isArray(payments.value)) return '0.00';
    const total = payments.value.reduce((sum, payment) => {
        const amount = parseFloat(payment.amount) || 0;
        return sum + amount;
    }, 0);
    return total.toFixed(2);
});

const paidCount = computed(() => {
    if (!Array.isArray(payments.value)) return 0;
    return payments.value.filter(p => p.status === 'paid').length;
});

const pendingCount = computed(() => {
    if (!Array.isArray(payments.value)) return 0;
    return payments.value.filter(p => p.status === 'pending').length;
});

const failedCount = computed(() => {
    if (!Array.isArray(payments.value)) return 0;
    return payments.value.filter(p => p.status === 'failed').length;
});

const viewPayment = async (paymentId) => {
    paymentDetailLoading.value = true;
    try {
        const response = await window.axios.get(`/payments-detail/${paymentId}`);
        selectedPayment.value = response.data;
        showPaymentModal.value = true;
    } catch (error) {
        console.error('Error fetching payment details:', error);
        if (error.response?.status === 403) {
            alert('You do not have permission to view payment details.');
        } else if (error.response?.status === 404) {
            alert('Payment not found.');
        } else {
            alert('Error loading payment details. Please try again.');
        }
    } finally {
        paymentDetailLoading.value = false;
    }
};

const closePaymentModal = () => {
    showPaymentModal.value = false;
    selectedPayment.value = null;
};

// Helper functions for formatting
const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    try {
        const date = new Date(dateString);
        return date.toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });
    } catch (error) {
        return dateString;
    }
};

const formatAppointmentDate = (dateString) => {
    if (!dateString) return 'N/A';
    try {
        const date = new Date(dateString);
        return date.toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        });
    } catch (error) {
        return dateString;
    }
};

const formatTimeSlot = (timeSlot) => {
    if (!timeSlot) return 'N/A';

    // If it's already a string, return it
    if (typeof timeSlot === 'string') {
        return timeSlot;
    }

    // If it's an object with start_time and end_time
    if (typeof timeSlot === 'object' && timeSlot.start_time && timeSlot.end_time) {
        return `${timeSlot.start_time} - ${timeSlot.end_time}`;
    }

    // Try to parse as JSON if it's a string representation
    try {
        const parsed = typeof timeSlot === 'string' ? JSON.parse(timeSlot) : timeSlot;
        if (parsed.start_time && parsed.end_time) {
            return `${parsed.start_time} - ${parsed.end_time}`;
        }
    } catch (error) {
        // If parsing fails, return the original value
        return timeSlot.toString();
    }

    return timeSlot.toString();
};

const formatAmount = (amount, currency = 'GBP') => {
    const numAmount = parseFloat(amount) || 0;
    const currencySymbols = {
        'USD': '$',
        'GBP': '£',
        'EUR': '€',
        'gbp': '£',
        'usd': '$',
        'eur': '€'
    };

    const symbol = currencySymbols[currency] || currencySymbols[currency?.toUpperCase()] || '£';
    return `${symbol}${numAmount.toFixed(2)}`;
};

const formatPaymentMethod = (type, details) => {
    if (!type) return 'N/A';

    const capitalizedType = type.charAt(0).toUpperCase() + type.slice(1);

    if (!details || details === 'unknown') {
        return capitalizedType;
    }

    return `${capitalizedType} ****${details}`;
};

const formatStatus = (status) => {
    if (!status) return 'N/A';

    const statusMap = {
        'succeeded': 'Succeeded',
        'pending': 'Pending',
        'failed': 'Failed',
        'refunded': 'Refunded',
        'paid': 'Paid'
    };

    return statusMap[status] || status.charAt(0).toUpperCase() + status.slice(1);
};

onMounted(() => {
    fetchPayments();
});
</script>

<template>
    <Head title="Payment Management" />

    <AppLayout>
        <template #header>
            <div class="flex items-center justify-between">
                <div>
                    <h2 class="text-xl font-semibold leading-tight text-gray-800 dark:text-gray-200">
                        Payment Management
                    </h2>
                    <nav class="flex mt-2" aria-label="Breadcrumb">
                        <ol class="inline-flex items-center space-x-1 md:space-x-3">
                            <li v-for="(breadcrumb, index) in breadcrumbs" :key="index" class="inline-flex items-center">
                                <Link v-if="index < breadcrumbs.length - 1" 
                                    :href="breadcrumb.href" 
                                    class="text-sm font-medium text-gray-500 hover:text-blue-600 dark:text-gray-400 dark:hover:text-white">
                                    {{ breadcrumb.title }}
                                </Link>
                                <span v-else class="text-sm font-medium text-gray-700 dark:text-gray-400">
                                    {{ breadcrumb.title }}
                                </span>
                                <svg v-if="index < breadcrumbs.length - 1" class="w-3 h-3 mx-1 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                                </svg>
                            </li>
                        </ol>
                    </nav>
                </div>
            </div>
        </template>

        <div class="py-12">
            <div class="mx-auto max-w-7xl sm:px-6 lg:px-8">
                <!-- Stats Cards -->
                <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
                    <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                        <div class="p-6">
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    <i class="fas fa-dollar-sign text-2xl text-green-500"></i>
                                </div>
                                <div class="ml-4">
                                    <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Total Revenue</p>
                                    <p class="text-2xl font-semibold text-gray-900 dark:text-gray-100">
                                        ${{ totalRevenue }}
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                        <div class="p-6">
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    <i class="fas fa-check-circle text-2xl text-green-600"></i>
                                </div>
                                <div class="ml-4">
                                    <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Paid</p>
                                    <p class="text-2xl font-semibold text-gray-900 dark:text-gray-100">
                                        {{ paidCount }}
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                        <div class="p-6">
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    <i class="fas fa-clock text-2xl text-yellow-500"></i>
                                </div>
                                <div class="ml-4">
                                    <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Pending</p>
                                    <p class="text-2xl font-semibold text-gray-900 dark:text-gray-100">
                                        {{ pendingCount }}
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                        <div class="p-6">
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    <i class="fas fa-times-circle text-2xl text-red-500"></i>
                                </div>
                                <div class="ml-4">
                                    <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Failed</p>
                                    <p class="text-2xl font-semibold text-gray-900 dark:text-gray-100">
                                        {{ failedCount }}
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Payments Table -->
                <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6 text-gray-900 dark:text-gray-100">
                        <div v-if="loading" class="text-center py-8">
                            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
                        </div>
                        
                        <div v-else class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                                <thead class="bg-gray-50 dark:bg-gray-700">
                                    <tr>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                            Transaction
                                        </th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                            Patient
                                        </th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                            Provider
                                        </th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                            Amount
                                        </th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                            Status
                                        </th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                            Date
                                        </th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                            Actions
                                        </th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                                    <tr v-for="payment in (Array.isArray(payments) ? payments : [])" :key="payment.id" class="hover:bg-gray-50 dark:hover:bg-gray-700">
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm font-medium text-gray-900 dark:text-gray-100">
                                                {{ payment.transaction_id }}
                                            </div>
                                            <div class="text-sm text-gray-500 dark:text-gray-400">
                                                {{ payment.payment_method }}
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm text-gray-900 dark:text-gray-100">{{ payment.patient_name }}</div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm text-gray-900 dark:text-gray-100">{{ payment.provider_name }}</div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm font-medium text-gray-900 dark:text-gray-100">
                                                ${{ (parseFloat(payment.amount) || 0).toFixed(2) }}
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span :class="getStatusBadgeClass(payment.status)" class="inline-flex px-2 py-1 text-xs font-semibold rounded-full">
                                                {{ payment.status }}
                                            </span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                                            {{ payment.date }}
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                            <button
                                                @click="viewPayment(payment.id)"
                                                :disabled="paymentDetailLoading"
                                                class="text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300 mr-3 disabled:opacity-50 disabled:cursor-not-allowed">
                                                <span v-if="paymentDetailLoading">Loading...</span>
                                                <span v-else>View</span>
                                            </button>
                                            <button v-if="payment.status === 'paid'" class="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300">
                                                Refund
                                            </button>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Payment Detail Modal -->
        <div v-if="showPaymentModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50" @click="closePaymentModal">
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto" @click.stop>
                <div class="p-6">
                    <!-- Modal Header -->
                    <div class="flex items-center justify-between mb-6">
                        <h3 class="text-xl font-semibold text-gray-900 dark:text-gray-100">Payment Details</h3>
                        <button @click="closePaymentModal" class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                        </button>
                    </div>

                    <!-- Payment Details Content -->
                    <div v-if="selectedPayment" class="space-y-6">
                        <!-- Transaction Information -->
                        <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                            <h4 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-3">Transaction Information</h4>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-500 dark:text-gray-400">Transaction ID</label>
                                    <p class="text-sm text-gray-900 dark:text-gray-100">{{ selectedPayment.payment_id }}</p>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-500 dark:text-gray-400">Amount</label>
                                    <p class="text-sm font-semibold text-gray-900 dark:text-gray-100">{{ formatAmount(selectedPayment.amount, selectedPayment.currency) }}</p>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-500 dark:text-gray-400">Status</label>
                                    <span :class="getStatusBadgeClass(selectedPayment.status)" class="inline-flex px-2 py-1 text-xs font-semibold rounded-full">
                                        {{ formatStatus(selectedPayment.status) }}
                                    </span>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-500 dark:text-gray-400">Currency</label>
                                    <p class="text-sm text-gray-900 dark:text-gray-100">{{ selectedPayment.currency || 'USD' }}</p>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-500 dark:text-gray-400">Payment Method</label>
                                    <p class="text-sm text-gray-900 dark:text-gray-100">{{ formatPaymentMethod(selectedPayment.payment_method_type, selectedPayment.payment_method_details) }}</p>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-500 dark:text-gray-400">Date</label>
                                    <p class="text-sm text-gray-900 dark:text-gray-100">{{ formatDate(selectedPayment.created_at) }}</p>
                                </div>
                            </div>
                        </div>

                        <!-- Patient Information -->
                        <div v-if="selectedPayment.user" class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                            <h4 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-3">Patient Information</h4>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-500 dark:text-gray-400">Name</label>
                                    <p class="text-sm text-gray-900 dark:text-gray-100">{{ selectedPayment.user.name }}</p>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-500 dark:text-gray-400">Email</label>
                                    <p class="text-sm text-gray-900 dark:text-gray-100">{{ selectedPayment.user.email }}</p>
                                </div>
                            </div>
                        </div>

                        <!-- Appointment Information -->
                        <div v-if="selectedPayment.appointment" class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                            <h4 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-3">Appointment Information</h4>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-500 dark:text-gray-400">Provider</label>
                                    <p class="text-sm text-gray-900 dark:text-gray-100">{{ selectedPayment.appointment.provider?.user?.name || 'N/A' }}</p>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-500 dark:text-gray-400">Service</label>
                                    <p class="text-sm text-gray-900 dark:text-gray-100">{{ selectedPayment.appointment.service?.name || 'General Consultation' }}</p>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-500 dark:text-gray-400">Date</label>
                                    <p class="text-sm text-gray-900 dark:text-gray-100">{{ formatAppointmentDate(selectedPayment.appointment.date) }}</p>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-500 dark:text-gray-400">Time</label>
                                    <p class="text-sm text-gray-900 dark:text-gray-100">{{ formatTimeSlot(selectedPayment.appointment.time_slot) }}</p>
                                </div>
                            </div>
                        </div>

                        <!-- Description -->
                        <div v-if="selectedPayment.description" class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                            <h4 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-3">Description</h4>
                            <p class="text-sm text-gray-900 dark:text-gray-100">{{ selectedPayment.description }}</p>
                        </div>

                        <!-- Metadata -->
                        <div v-if="selectedPayment.metadata && Object.keys(selectedPayment.metadata).length > 0" class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                            <h4 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-3">Additional Information</h4>
                            <div class="space-y-2">
                                <div v-for="(value, key) in selectedPayment.metadata" :key="key" class="flex justify-between">
                                    <span class="text-sm font-medium text-gray-500 dark:text-gray-400 capitalize">{{ key.replace('_', ' ') }}:</span>
                                    <span class="text-sm text-gray-900 dark:text-gray-100">{{ value }}</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Loading State -->
                    <div v-else class="text-center py-8">
                        <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
                        <p class="mt-2 text-gray-600 dark:text-gray-400">Loading payment details...</p>
                    </div>

                    <!-- Modal Footer -->
                    <div class="mt-6 flex justify-end">
                        <button @click="closePaymentModal" class="px-4 py-2 bg-gray-300 dark:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-400 dark:hover:bg-gray-500 transition-colors">
                            Close
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </AppLayout>
</template>
