<script setup>
import AppLayout from '@/layouts/AppLayout.vue'
import { Head, Link, router, usePage } from '@inertiajs/vue3'
import { ref, computed, onMounted } from 'vue'
import axios from 'axios'
import AppointmentBookingModal from '@/components/AppointmentBookingModal.vue'

// Get user from page props
const page = usePage()
const user = computed(() => page.props.auth?.user)

// Shop is now available for all users
const isPatient = computed(() => user.value?.role === 'patient')

const breadcrumbs = [
    {
        title: 'Shop',
        href: '/shop',
    },
]

// Reactive data
const searchQuery = ref('')
const selectedCategory = ref('all')
const sortBy = ref('name')
const loading = ref(false)
const providersLoading = ref(false)

// Main sections
const activeSection = ref('products') // 'products' or 'services'
const selectedProvider = ref(null)
const showProviderServices = ref(false)
const showBookingModal = ref(false)
const selectedService = ref(null)

// Data
const providers = ref([])
const providerServices = ref([])
const products = ref([])
const categories = ref([])
const cartCount = ref(0)

// Service categories (specializations)
const serviceCategories = ref([
    { id: 'all', name: 'All Specializations', icon: '🏥' },
    { id: 'general-practice', name: 'General Practice', icon: '👨‍⚕️' },
    { id: 'cardiology', name: 'Cardiology', icon: '❤️' },
    { id: 'dermatology', name: 'Dermatology', icon: '🧴' },
    { id: 'psychiatry', name: 'Psychiatry', icon: '🧠' },
    { id: 'pediatrics', name: 'Pediatrics', icon: '👶' },
    { id: 'orthopedics', name: 'Orthopedics', icon: '🦴' },
    { id: 'gynecology', name: 'Gynecology', icon: '👩‍⚕️' },
    { id: 'neurology', name: 'Neurology', icon: '🧠' },
    { id: 'oncology', name: 'Oncology', icon: '🎗️' },
    { id: 'ophthalmology', name: 'Ophthalmology', icon: '👁️' }
])

// Load providers from API
const loadProviders = async () => {
    loading.value = true
    try {
        const response = await axios.get('/providers-list')
        providers.value = response.data.data || response.data.providers || response.data || []
    } catch (error) {
        console.error('Error loading providers:', error)
        providers.value = []
    } finally {
        loading.value = false
    }
}

// Load products from API
const loadProducts = async () => {
    loading.value = true
    try {
        const params = {
            category: selectedCategory.value !== 'all' ? selectedCategory.value : '',
            search: searchQuery.value,
            sort: sortBy.value
        }
        const response = await axios.get('/shop/products', { params })
        products.value = response.data.products?.data || response.data.products || []
    } catch (error) {
        console.error('Error loading products:', error)
        products.value = []
    } finally {
        loading.value = false
    }
}

// Load product categories
const loadCategories = async () => {
    try {
        const response = await axios.get('/shop/categories')
        categories.value = response.data.categories || response.data || []
    } catch (error) {
        console.error('Error loading categories:', error)
        categories.value = []
    }
}

// Load cart count
const loadCartCount = async () => {
    try {
        const response = await axios.get('/shop/cart/count')
        cartCount.value = response.data.cart_count || 0
    } catch (error) {
        console.error('Error loading cart count:', error)
        cartCount.value = 0
    }
}

// Load provider services
const loadProviderServices = async (providerId) => {
    providersLoading.value = true
    try {
        const response = await axios.get(`/get-providers/${providerId}/services`)
        providerServices.value = response.data.services || []
    } catch (error) {
        console.error('Error loading provider services:', error)
        providerServices.value = []
    } finally {
        providersLoading.value = false
    }
}

// Computed properties
const filteredProviders = computed(() => {
    let filtered = providers.value

    // Filter by specialization
    if (selectedCategory.value !== 'all') {
        const categoryName = selectedCategory.value.replace('-', ' ')
        filtered = filtered.filter(provider => {
            const specialization = provider.specialization?.toLowerCase() || ''

            // More flexible matching for specializations
            return specialization.includes(categoryName) ||
                   (specialization.includes('general') && categoryName.includes('general')) ||
                   specialization === selectedCategory.value
        })
    }

    // Filter by search query
    if (searchQuery.value.trim()) {
        const query = searchQuery.value.toLowerCase()
        filtered = filtered.filter(provider =>
            provider.user?.name?.toLowerCase().includes(query) ||
            provider.specialization?.toLowerCase().includes(query) ||
            provider.bio?.toLowerCase().includes(query)
        )
    }

    // Sort providers
    switch (sortBy.value) {
        case 'name':
        default:
            filtered.sort((a, b) => (a.user?.name || '').localeCompare(b.user?.name || ''))
            break
    }

    return filtered
})

const getAvailableDays = (provider) => {
    if (!provider.weekly_availability) return 'Not specified'

    const days = Object.keys(provider.weekly_availability).filter(day =>
        provider.weekly_availability[day] && provider.weekly_availability[day].length > 0
    )

    if (days.length === 0) return 'Not available'
    if (days.length === 7) return 'Every day'

    return days.map(day => day.charAt(0).toUpperCase() + day.slice(1)).join(', ')
}

const getProviderLocation = (provider) => {
    if (!provider.practice_locations || provider.practice_locations.length === 0) {
        return 'Location not specified'
    }
    return provider.practice_locations[0]
}

// Methods
const switchSection = (section) => {
    activeSection.value = section
    selectedCategory.value = 'all'
    searchQuery.value = ''
    selectedProvider.value = null
    showProviderServices.value = false

    if (section === 'services') {
        loadProviders()
    } else if (section === 'products') {
        loadProducts()
        loadCategories()
    }
}

// Add product to cart
const addToCart = async (product, quantity = 1) => {
    try {
        const response = await axios.post('/shop/cart/add', {
            product_id: product.id,
            quantity: quantity
        })

        if (response.data.success) {
            cartCount.value = response.data.cart_count
            // Show success message
            alert('Product added to cart!')
        } else {
            alert(response.data.message || 'Failed to add product to cart')
        }
    } catch (error) {
        console.error('Error adding to cart:', error)
        alert('Failed to add product to cart')
    }
}

const selectProvider = async (provider) => {
    selectedProvider.value = provider
    showProviderServices.value = true
    await loadProviderServices(provider.id)
}

const backToProviders = () => {
    selectedProvider.value = null
    showProviderServices.value = false
    providerServices.value = []
}

const bookService = (service) => {
    selectedService.value = service
    showBookingModal.value = true
}

const formatPrice = (price) => {
    return new Intl.NumberFormat('en-GB', {
        style: 'currency',
        currency: 'GBP'
    }).format(price)
}

const formatRating = (rating) => {
    return rating ? rating.toFixed(1) : 'N/A'
}

// Initialize
onMounted(() => {
    loadProducts()
    loadCategories()
    loadCartCount()
})
</script>

<template>
    <Head title="Shop - Medroid" />

    <AppLayout :breadcrumbs="breadcrumbs">
        <!-- Shop Content -->
        <div class="py-12">
            <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
                <!-- Header -->
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
                    <div class="p-6 bg-white border-b border-gray-200">
                        <div class="flex items-center justify-between">
                            <div>
                                <h1 class="text-2xl font-bold text-gray-900">Health Shop</h1>
                                <p class="text-gray-600 mt-1">Browse healthcare providers and book medical services</p>

                                <!-- Section Switcher -->
                                <div class="flex mt-4 bg-gray-100 rounded-lg p-1 max-w-md">
                                    <button
                                        @click="switchSection('products')"
                                        :class="[
                                            'flex-1 px-4 py-2 rounded-md text-sm font-medium transition-colors',
                                            activeSection === 'products'
                                                ? 'bg-white text-blue-600 shadow-sm'
                                                : 'text-gray-600 hover:text-gray-900'
                                        ]"
                                    >
                                        🛒 Products
                                    </button>
                                    <button
                                        @click="switchSection('services')"
                                        :class="[
                                            'flex-1 px-4 py-2 rounded-md text-sm font-medium transition-colors',
                                            activeSection === 'services'
                                                ? 'bg-white text-purple-600 shadow-sm'
                                                : 'text-gray-600 hover:text-gray-900'
                                        ]"
                                    >
                                        🩺 Services
                                    </button>
                                </div>
                            </div>
                            <div class="flex items-center space-x-4">
                                <div class="relative">
                                    <input
                                        v-model="searchQuery"
                                        type="text"
                                        :placeholder="activeSection === 'products' ? 'Search products...' : 'Search providers...'"
                                        class="w-64 pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                                        @input="activeSection === 'products' ? loadProducts() : loadProviders()"
                                    />
                                    <svg class="absolute left-3 top-2.5 h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                                    </svg>
                                </div>

                                <!-- Cart Icon -->
                                <Link
                                    v-if="activeSection === 'products'"
                                    href="/shop/cart"
                                    class="relative inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                                >
                                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-2.5 5M7 13l2.5 5m6-5v6a2 2 0 01-2 2H9a2 2 0 01-2-2v-6m8 0V9a2 2 0 00-2-2H9a2 2 0 00-2 2v4.01"></path>
                                    </svg>
                                    Cart
                                    <span v-if="cartCount > 0" class="absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full h-6 w-6 flex items-center justify-center">
                                        {{ cartCount }}
                                    </span>
                                </Link>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Products Section -->
                <div v-if="activeSection === 'products'">
                    <!-- Product Categories Filter -->
                    <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
                        <div class="p-4">
                            <h2 class="text-lg font-semibold text-gray-900 mb-3">Browse by Category</h2>
                            <div class="grid grid-cols-3 md:grid-cols-5 lg:grid-cols-8 xl:grid-cols-10 gap-3">
                                <button
                                    @click="selectedCategory = 'all'; loadProducts()"
                                    :class="[
                                        'p-3 rounded-lg border transition-colors duration-200 text-center hover:shadow-sm',
                                        selectedCategory === 'all'
                                            ? 'border-blue-500 bg-blue-50 text-blue-700 shadow-sm'
                                            : 'border-gray-200 hover:border-blue-300 text-gray-700 hover:bg-gray-50'
                                    ]"
                                >
                                    <div class="text-lg mb-1">🏪</div>
                                    <div class="text-xs font-medium leading-tight">All Products</div>
                                </button>
                                <button
                                    v-for="category in categories"
                                    :key="category.id"
                                    @click="selectedCategory = category.id; loadProducts()"
                                    :class="[
                                        'p-3 rounded-lg border transition-colors duration-200 text-center hover:shadow-sm',
                                        selectedCategory === category.id
                                            ? 'border-blue-500 bg-blue-50 text-blue-700 shadow-sm'
                                            : 'border-gray-200 hover:border-blue-300 text-gray-700 hover:bg-gray-50'
                                    ]"
                                >
                                    <div class="text-lg mb-1">{{ category.icon || '📦' }}</div>
                                    <div class="text-xs font-medium leading-tight">{{ category.name }}</div>
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Products List -->
                    <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                        <div class="p-6">
                            <div class="flex items-center justify-between mb-6">
                                <h2 class="text-lg font-semibold text-gray-900">Products</h2>
                                <select
                                    v-model="sortBy"
                                    @change="loadProducts()"
                                    class="border border-gray-300 rounded-lg px-3 py-2 text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                >
                                    <option value="name">Sort by Name</option>
                                    <option value="price_low">Price: Low to High</option>
                                    <option value="price_high">Price: High to Low</option>
                                    <option value="newest">Newest First</option>
                                </select>
                            </div>

                            <!-- Loading State -->
                            <div v-if="loading" class="text-center py-12">
                                <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                                <p class="mt-2 text-gray-600">Loading products...</p>
                            </div>

                            <!-- Products Grid -->
                            <div v-else-if="products.length > 0" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                                <div
                                    v-for="product in products"
                                    :key="product.id"
                                    class="bg-white border border-gray-200 rounded-lg overflow-hidden hover:shadow-lg transition-shadow duration-200"
                                >
                                    <!-- Product Image -->
                                    <div class="aspect-w-1 aspect-h-1 w-full overflow-hidden bg-gray-200 relative">
                                        <img
                                            v-if="product.primary_image"
                                            :src="`/storage/${product.primary_image}`"
                                            :alt="product.name"
                                            class="h-48 w-full object-cover object-center group-hover:opacity-75"
                                        />
                                        <div v-else class="h-48 w-full flex items-center justify-center bg-gray-200">
                                            <i class="fas fa-image text-gray-400 text-4xl"></i>
                                        </div>
                                        <div v-if="product.is_on_sale" class="absolute top-3 left-3 bg-red-500 text-white text-xs px-2 py-1 rounded shadow-sm">
                                            {{ product.discount_percentage }}% OFF
                                        </div>
                                        <div v-if="product.type === 'digital'" class="absolute top-3 right-3">
                                            <span class="bg-purple-500 text-white text-xs px-2 py-1 rounded shadow-sm">
                                                Digital
                                            </span>
                                        </div>
                                    </div>

                                    <!-- Product Info -->
                                    <div class="p-4">
                                        <h3 class="text-lg font-semibold text-gray-900 mb-2">{{ product.name }}</h3>
                                        <p class="text-gray-600 text-sm mb-3 line-clamp-2">{{ product.short_description }}</p>

                                        <!-- Price -->
                                        <div class="flex items-center justify-between mb-4">
                                            <div class="flex items-center space-x-2">
                                                <span class="text-lg font-bold text-blue-600">{{ product.formatted_price }}</span>
                                                <span v-if="product.is_on_sale" class="text-sm text-gray-500 line-through">{{ product.formatted_original_price }}</span>
                                            </div>
                                            <span v-if="product.type === 'physical' && product.stock_quantity <= 5" class="text-xs text-orange-600">
                                                Only {{ product.stock_quantity }} left
                                            </span>
                                        </div>

                                        <!-- Add to Cart Button -->
                                        <button
                                            @click="addToCart(product)"
                                            :disabled="!product.can_purchase"
                                            :class="[
                                                'w-full py-2 px-4 rounded-lg transition-colors font-medium',
                                                product.can_purchase
                                                    ? 'bg-blue-600 text-white hover:bg-blue-700'
                                                    : 'bg-gray-300 text-gray-500 cursor-not-allowed'
                                            ]"
                                        >
                                            <i class="fas fa-shopping-cart mr-2"></i>
                                            {{ product.can_purchase ? 'Add to Cart' : 'Out of Stock' }}
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <!-- Empty State -->
                            <div v-else class="text-center py-12">
                                <i class="fas fa-box-open text-4xl text-gray-400 mb-4"></i>
                                <h3 class="text-lg font-medium text-gray-900 mb-2">No products found</h3>
                                <p class="text-gray-500">Try adjusting your search or filter criteria.</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Services Section -->
                <div v-if="activeSection === 'services'">
                    <!-- Back Button (when viewing provider services) -->
                    <div v-if="showProviderServices" class="mb-6">
                        <button
                            @click="backToProviders"
                            class="inline-flex items-center px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors"
                        >
                            <i class="fas fa-arrow-left mr-2"></i>
                            Back to Providers
                        </button>
                    </div>

                    <!-- Specializations Filter -->
                    <div v-if="!showProviderServices" class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
                        <div class="p-4">
                            <h2 class="text-lg font-semibold text-gray-900 mb-3">Browse by Specialization</h2>
                            <div class="grid grid-cols-3 md:grid-cols-5 lg:grid-cols-8 xl:grid-cols-10 gap-3">
                                <button
                                    v-for="category in serviceCategories"
                                    :key="category.id"
                                    @click="selectedCategory = category.id"
                                    :class="[
                                        'p-3 rounded-lg border transition-colors duration-200 text-center hover:shadow-sm',
                                        selectedCategory === category.id
                                            ? 'border-purple-500 bg-purple-50 text-purple-700 shadow-sm'
                                            : 'border-gray-200 hover:border-purple-300 text-gray-700 hover:bg-gray-50'
                                    ]"
                                >
                                    <div class="text-lg mb-1">{{ category.icon }}</div>
                                    <div class="text-xs font-medium leading-tight">{{ category.name }}</div>
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Providers List -->
                    <div v-if="!showProviderServices" class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                        <div class="p-6">
                            <div class="flex items-center justify-between mb-6">
                                <h2 class="text-lg font-semibold text-gray-900">Healthcare Providers</h2>
                                <select
                                    v-model="sortBy"
                                    class="border border-gray-300 rounded-lg px-3 py-2 text-sm focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                                >
                                    <option value="name">Sort by Name</option>
                                </select>
                            </div>

                            <!-- Loading State -->
                            <div v-if="loading" class="text-center py-12">
                                <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600"></div>
                                <p class="mt-2 text-gray-600">Loading providers...</p>
                            </div>

                            <!-- Providers Grid -->
                            <div v-else-if="filteredProviders.length > 0" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                                <div
                                    v-for="provider in filteredProviders"
                                    :key="provider.id"
                                    class="bg-white border border-gray-200 rounded-lg overflow-hidden hover:shadow-lg transition-shadow duration-200 cursor-pointer"
                                    @click="selectProvider(provider)"
                                >
                                    <!-- Provider Header -->
                                    <div class="p-6">
                                        <div class="flex items-center justify-between mb-4">
                                            <div class="flex items-center space-x-3">
                                                <div class="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center">
                                                    <i class="fas fa-user-md text-purple-600 text-xl"></i>
                                                </div>
                                                <div class="flex-1">
                                                    <h3 class="text-lg font-semibold text-gray-900">
                                                        Dr. {{ provider.user?.name || 'Provider' }}
                                                    </h3>
                                                    <p class="text-sm text-purple-600 font-medium">
                                                        {{ provider.specialization || 'General Practice' }}
                                                    </p>
                                                </div>
                                            </div>

                                        </div>

                                        <!-- Provider Details -->
                                        <div class="space-y-2 mb-4">
                                            <div v-if="provider.bio" class="text-sm text-gray-600 leading-relaxed">
                                                <p class="line-clamp-3 break-words">{{ provider.bio }}</p>
                                            </div>

                                            <div class="flex items-center text-sm text-gray-500">
                                                <i class="fas fa-map-marker-alt mr-2 text-gray-400"></i>
                                                <span>{{ getProviderLocation(provider) }}</span>
                                            </div>

                                            <div class="flex items-center text-sm text-gray-500">
                                                <i class="fas fa-venus-mars mr-2 text-gray-400"></i>
                                                <span class="capitalize">{{ provider.gender || 'Not specified' }}</span>
                                            </div>

                                            <div class="flex items-center text-sm text-gray-500">
                                                <i class="fas fa-calendar mr-2 text-gray-400"></i>
                                                <span>{{ getAvailableDays(provider) }}</span>
                                            </div>

                                            <div v-if="provider.languages && provider.languages.length > 0" class="flex items-center text-sm text-gray-500">
                                                <i class="fas fa-language mr-2 text-gray-400"></i>
                                                <span>{{ provider.languages.slice(0, 2).join(', ') }}{{ provider.languages.length > 2 ? '...' : '' }}</span>
                                            </div>
                                        </div>

                                        <!-- Pricing -->
                                        <div v-if="provider.pricing" class="mb-4">
                                            <div class="flex items-center justify-between text-sm">
                                                <span class="text-gray-600">Consultation:</span>
                                                <span class="font-semibold text-purple-600">
                                                    {{ formatPrice(provider.pricing.consultation || 0) }}
                                                </span>
                                            </div>
                                        </div>

                                        <!-- Action Button -->
                                        <button class="w-full bg-purple-600 text-white py-2 px-4 rounded-lg hover:bg-purple-700 transition-colors font-medium">
                                            <i class="fas fa-eye mr-2"></i>
                                            View Services
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <!-- Empty State -->
                            <div v-else class="text-center py-12">
                                <i class="fas fa-user-md text-4xl text-gray-400 mb-4"></i>
                                <h3 class="text-lg font-medium text-gray-900 mb-2">No providers found</h3>
                                <p class="text-gray-500">Try adjusting your search or filter criteria.</p>
                            </div>
                        </div>
                    </div>

                    <!-- Provider Services View -->
                    <div v-else class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                        <div class="p-6">
                            <!-- Provider Header -->
                            <div class="border-b border-gray-200 pb-6 mb-6">
                                <div class="flex items-center space-x-4">
                                    <div class="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center">
                                        <i class="fas fa-user-md text-purple-600 text-2xl"></i>
                                    </div>
                                    <div class="flex-1">
                                        <h2 class="text-2xl font-bold text-gray-900">
                                            Dr. {{ selectedProvider?.user?.name || 'Provider' }}
                                        </h2>
                                        <p class="text-purple-600 font-medium">{{ selectedProvider?.specialization }}</p>
                                        <div class="flex items-center mt-2 space-x-4 text-sm text-gray-500">
                                            <span><i class="fas fa-map-marker-alt mr-1"></i>{{ getProviderLocation(selectedProvider) }}</span>
                                        </div>
                                    </div>
                                </div>
                                <div v-if="selectedProvider?.bio" class="mt-4">
                                    <p class="text-gray-600 leading-relaxed break-words">{{ selectedProvider.bio }}</p>
                                </div>
                            </div>

                            <!-- Services Loading -->
                            <div v-if="providersLoading" class="text-center py-12">
                                <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600"></div>
                                <p class="mt-2 text-gray-600">Loading services...</p>
                            </div>

                            <!-- Services Grid -->
                            <div v-else-if="providerServices.length > 0">
                                <h3 class="text-lg font-semibold text-gray-900 mb-4">Available Services</h3>
                                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                                    <div
                                        v-for="service in providerServices"
                                        :key="service.id"
                                        class="bg-white border border-gray-200 rounded-lg overflow-hidden hover:shadow-lg transition-shadow duration-200"
                                    >
                                        <div class="p-6">
                                            <div class="flex items-center justify-between mb-3">
                                                <span class="text-xs font-medium text-purple-600 bg-purple-100 px-2 py-1 rounded-full">
                                                    {{ service.category || 'Service' }}
                                                </span>
                                                <span v-if="service.is_telemedicine" class="text-xs font-medium text-blue-600 bg-blue-100 px-2 py-1 rounded-full">
                                                    Video Call
                                                </span>
                                            </div>

                                            <h4 class="text-lg font-semibold text-gray-900 mb-2">{{ service.name }}</h4>
                                            <p class="text-gray-600 text-sm mb-4 line-clamp-3">{{ service.description }}</p>

                                            <div class="space-y-2 mb-4">
                                                <div class="flex items-center justify-between text-sm">
                                                    <span class="text-gray-500">Duration:</span>
                                                    <span class="font-medium">{{ service.duration }} minutes</span>
                                                </div>
                                                <div class="flex items-center justify-between text-sm">
                                                    <span class="text-gray-500">Price:</span>
                                                    <span class="font-bold text-purple-600 text-lg">{{ formatPrice(service.price) }}</span>
                                                </div>
                                            </div>

                                            <button
                                                @click="bookService(service)"
                                                :disabled="!service.active"
                                                :class="[
                                                    'w-full py-2 px-4 rounded-lg transition-colors font-medium',
                                                    service.active
                                                        ? 'bg-purple-600 text-white hover:bg-purple-700'
                                                        : 'bg-gray-300 text-gray-500 cursor-not-allowed'
                                                ]"
                                            >
                                                <i class="fas fa-calendar-plus mr-2"></i>
                                                {{ service.active ? 'Book Appointment' : 'Unavailable' }}
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- No Services -->
                            <div v-else class="text-center py-12">
                                <i class="fas fa-stethoscope text-4xl text-gray-400 mb-4"></i>
                                <h3 class="text-lg font-medium text-gray-900 mb-2">No services available</h3>
                                <p class="text-gray-500">This provider hasn't listed any services yet.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- End of Original Shop Content -->

        <!-- Appointment Booking Modal -->
        <AppointmentBookingModal
            :is-open="showBookingModal"
            :service="selectedService"
            :provider="selectedProvider"
            @close="showBookingModal = false; selectedService = null"
            @booked="showBookingModal = false; selectedService = null"
        />
    </AppLayout>
</template>
