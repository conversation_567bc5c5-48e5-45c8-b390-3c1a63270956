import vue from '@vitejs/plugin-vue';
import laravel from 'laravel-vite-plugin';
import path from 'path';
import tailwindcss from "@tailwindcss/vite";
import { resolve } from 'node:path';
import { defineConfig } from 'vite';

export default defineConfig({
    plugins: [
        laravel({
            input: [
                'resources/css/app.css',
                'resources/js/app.ts'
            ],
            ssr: 'resources/js/ssr.ts',
            refresh: true,
        }),
        tailwindcss(),
        vue({
            template: {
                transformAssetUrls: {
                    base: null,
                    includeAbsolute: false,
                },
            },
        }),
    ],
    server: {
        host: '0.0.0.0', // Allow external connections
        port: 5174,
        cors: {
            origin: [
                'https://tightly-assuring-mongoose.ngrok-free.app',
                'http://localhost:8000',
                'http://127.0.0.1:8000',
                /^https:\/\/.*\.ngrok-free\.app$/,
                /^https:\/\/.*\.ngrok\.io$/
            ],
            credentials: true
        },
        hmr: {
            host: 'localhost',
            port: 5174
        }
    },
    resolve: {
        alias: {
            '@': path.resolve(__dirname, './resources/js'),
            'ziggy-js': resolve(__dirname, 'vendor/tightenco/ziggy'),
        },
    },
    build: {
        chunkSizeWarningLimit: 1000,
        rollupOptions: {
            output: {
                manualChunks: {
                    vendor: ['vue', '@inertiajs/vue3', 'ziggy-js'],
                }
            }
        }
    }
});
